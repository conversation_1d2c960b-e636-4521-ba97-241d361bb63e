package com.ruoyi.quartz.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;
import com.ruoyi.common.config.RuoYiConfig;
import org.springframework.stereotype.Component;
import com.ruoyi.common.utils.StringUtils;

import java.io.File;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时任务调度测试
 * 
 * <AUTHOR>
 */
@Component("ryTask")
public class RyTask
{
    public void ryMultipleParams(String s, <PERSON><PERSON><PERSON> b, <PERSON> l, Double d, Integer i)
    {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void ryParams(String params)
    {
        System.out.println("执行有参方法：" + params);
    }

    public void ryNoParams()
    {
        System.out.println("执行无参方法");
    }

    /**
     *
     * @param host：数据库Ip
     * @param port：数据库端口
     * @param username：数据库账号
     * @param password：数据库密码
     * @param databasename：数据库名称
     * @param backupNumStr：备份保留份数
     * @throws Exception
     */
    public void mySqlDump(String host, String port, String username, String password, String databasename,String backupNumStr) throws Exception {

        String dir = RuoYiConfig.getDataBaseBackUp();
        if (!FileUtil.exist(dir)) {
            FileUtil.mkdir(dir);
        }

        String sqlName = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN)+ ".sql";
        String sqlPathName = dir + File.separator + sqlName;
        if (FileUtil.exist(sqlPathName)) {
            System.out.println(sqlName + "文件名已存在，请更换");
            return;
        }

        //拼接cmd命令  windows下 cmd   Linux下 /bin/sh
        Process exec;
        OsInfo osInfo = SystemUtil.getOsInfo();
        //如果不能取得系统属性os.name（因为Java安全限制），则总是返回false
        String command = null;
        if(osInfo.isWindows()){
            command="D:/phpstudy_pro/Extensions/MySQL5.7.26/bin/mysqldump -h " + host + " -P " + port +  " " + databasename + " -r " + sqlPathName;
            exec = Runtime.getRuntime().exec(command);
        } else {
            exec = Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", "/usr/bin/mysqldump -h" + host + " -P" + port + " -u " + username + " -p" + password + " " + databasename + " > " + sqlPathName});
        }
        if (exec.waitFor() != 0) {// 0 表示线程正常终止。
            throw new Exception("指令未成功执行，process.waitFor结果为："+exec.waitFor()+" 指令："+command);
        }

        //压缩
        String zipPathName = dir + File.separator + sqlName + ".zip";
        ZipUtil.zip(sqlPathName, zipPathName);
        //删除
        FileUtil.del(sqlPathName);

        //获取外部的备份保留份数
        int backupNum = 5;
        try {
            backupNum = NumberUtil.parseInt(backupNumStr);
        } catch (Exception e){
            System.out.println("backupNumStr非数字:"+backupNumStr);
        }

        //删除之前备份份数
        List<String> fileNames = FileUtil.listFileNames(dir);
        if(CollUtil.isNotEmpty(fileNames)){
            //从大到小排序
            fileNames = fileNames.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
            for (int i = 0; i < fileNames.size(); i++) {
                //超过的份数全删除
                if(i>=backupNum){
                    FileUtil.del(dir + File.separator + fileNames.get(i));
                }
            }
        }

    }

}
