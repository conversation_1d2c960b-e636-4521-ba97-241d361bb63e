import request from '@/utils/request'

// 查询竞赛指导教师列表
export function listInstructor(query) {
  return request({
    url: '/web/instructor/list',
    method: 'get',
    params: query
  })
}

// 查询竞赛指导教师详细
export function getInstructor(id) {
  return request({
    url: '/web/instructor/' + id,
    method: 'get'
  })
}

// 新增竞赛指导教师
export function addInstructor(data) {
  return request({
    url: '/web/instructor',
    method: 'post',
    data: data
  })
}

// 修改竞赛指导教师
export function updateInstructor(data) {
  return request({
    url: '/web/instructor',
    method: 'put',
    data: data
  })
}

// 删除竞赛指导教师
export function delInstructor(id) {
  return request({
    url: '/web/instructor/' + id,
    method: 'delete'
  })
}
