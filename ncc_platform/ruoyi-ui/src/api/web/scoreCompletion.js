import request from '@/utils/request'

// 查询积分完成情况列表
export function listScoreCompletion(query) {
  return request({
    url: '/web/scoreCompletion/list',
    method: 'get',
    params: query
  })
}

// 查询积分明细
export function getScoreDetails(query) {
  return request({
    url: '/web/scoreCompletion/details',
    method: 'get',
    params: query
  })
}

// 导出积分完成情况
export function exportScoreCompletion(query) {
  return request({
    url: '/web/scoreCompletion/export',
    method: 'get',
    params: query
  })
} 