import request from '@/utils/request'

// 查询立项信息列表
export function listLixiang(query) {
  return request({
    url: '/web/lixiang/list',
    method: 'get',
    params: query
  })
}

// 查询立项信息详细
export function getLixiang(id) {
  return request({
    url: '/web/lixiang/' + id,
    method: 'get'
  })
}

// 新增立项信息
export function addLixiang(data) {
  return request({
    url: '/web/lixiang',
    method: 'post',
    data: data
  })
}

// 修改立项信息
export function updateLixiang(data) {
  return request({
    url: '/web/lixiang',
    method: 'put',
    data: data
  })
}

// 删除立项信息
export function delLixiang(id) {
  return request({
    url: '/web/lixiang/' + id,
    method: 'delete'
  })
}
