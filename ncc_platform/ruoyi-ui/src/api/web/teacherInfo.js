import request from '@/utils/request'

// 查询教师信息列表
export function listTeacherInfo(query) {
  return request({
    url: '/web/teacherInfo/list',
    method: 'get',
    params: query
  })
}

// 查询教师信息详细
export function getTeacherInfo(id) {
  return request({
    url: '/web/teacherInfo/' + id,
    method: 'get'
  })
}

// 新增教师信息
export function addTeacherInfo(data) {
  return request({
    url: '/web/teacherInfo',
    method: 'post',
    data: data
  })
}

// 修改教师信息
export function updateTeacherInfo(data) {
  return request({
    url: '/web/teacherInfo',
    method: 'put',
    data: data
  })
}

// 删除教师信息
export function delTeacherInfo(id) {
  return request({
    url: '/web/teacherInfo/' + id,
    method: 'delete'
  })
}
