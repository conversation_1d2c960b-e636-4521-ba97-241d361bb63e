import request from '@/utils/request'

// 查询竞赛信息基础信息列表
export function listCompetitionBaseInfo(query) {
  return request({
    url: '/web/competitionBaseInfo/list',
    method: 'get',
    params: query
  })
}

// 查询竞赛信息基础信息详细
export function getCompetitionBaseInfo(id) {
  return request({
    url: '/web/competitionBaseInfo/' + id,
    method: 'get'
  })
}

// 新增竞赛信息基础信息
export function addCompetitionBaseInfo(data) {
  return request({
    url: '/web/competitionBaseInfo',
    method: 'post',
    data: data
  })
}

// 修改竞赛信息基础信息
export function updateCompetitionBaseInfo(data) {
  return request({
    url: '/web/competitionBaseInfo',
    method: 'put',
    data: data
  })
}

// 删除竞赛信息基础信息
export function delCompetitionBaseInfo(id) {
  return request({
    url: '/web/competitionBaseInfo/' + id,
    method: 'delete'
  })
}
