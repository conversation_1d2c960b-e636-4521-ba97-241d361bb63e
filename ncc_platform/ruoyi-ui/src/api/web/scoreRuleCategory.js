import request from '@/utils/request'

// 查询积分规则分类列表
export function listScoreRuleCategory(query) {
  return request({
    url: '/web/scoreRuleCategory/list',
    method: 'get',
    params: query
  })
}

// 查询积分规则分类详细
export function getScoreRuleCategory(id) {
  return request({
    url: '/web/scoreRuleCategory/' + id,
    method: 'get'
  })
}

// 新增积分规则分类
export function addScoreRuleCategory(data) {
  return request({
    url: '/web/scoreRuleCategory',
    method: 'post',
    data: data
  })
}

// 修改积分规则分类
export function updateScoreRuleCategory(data) {
  return request({
    url: '/web/scoreRuleCategory',
    method: 'put',
    data: data
  })
}

// 删除积分规则分类
export function delScoreRuleCategory(id) {
  return request({
    url: '/web/scoreRuleCategory/' + id,
    method: 'delete'
  })
}

// 获取完整分类树
export function getFullTree() {
  return request({
    url: '/web/scoreRuleCategory/tree',
    method: 'get'
  })
}
