import request from '@/utils/request'

// 查询教研室信息列表
export function listTrOfficeInfo(query) {
  return request({
    url: '/web/trOfficeInfo/list',
    method: 'get',
    params: query
  })
}

// 查询教研室信息详细
export function getTrOfficeInfo(id) {
  return request({
    url: '/web/trOfficeInfo/' + id,
    method: 'get'
  })
}

// 新增教研室信息
export function addTrOfficeInfo(data) {
  return request({
    url: '/web/trOfficeInfo',
    method: 'post',
    data: data
  })
}

// 修改教研室信息
export function updateTrOfficeInfo(data) {
  return request({
    url: '/web/trOfficeInfo',
    method: 'put',
    data: data
  })
}

// 删除教研室信息
export function delTrOfficeInfo(id) {
  return request({
    url: '/web/trOfficeInfo/' + id,
    method: 'delete'
  })
}
