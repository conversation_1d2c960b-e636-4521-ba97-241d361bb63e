import request from '@/utils/request'

// 查询结项信息列表
export function listJiexiang(query) {
  return request({
    url: '/web/jiexiang/list',
    method: 'get',
    params: query
  })
}

// 查询结项信息详细
export function getJiexiang(id) {
  return request({
    url: '/web/jiexiang/' + id,
    method: 'get'
  })
}

// 新增结项信息
export function addJiexiang(data) {
  return request({
    url: '/web/jiexiang',
    method: 'post',
    data: data
  })
}

// 修改结项信息
export function updateJiexiang(data) {
  return request({
    url: '/web/jiexiang',
    method: 'put',
    data: data
  })
}

// 删除结项信息
export function delJiexiang(id) {
  return request({
    url: '/web/jiexiang/' + id,
    method: 'delete'
  })
}
