import request from '@/utils/request'

// 查询审核记录列表
export function listAuditRecord(query) {
  return request({
    url: '/web/auditRecord/list',
    method: 'get',
    params: query
  })
}

// 查询审核记录详细
export function getAuditRecord(id) {
  return request({
    url: '/web/auditRecord/' + id,
    method: 'get'
  })
}

// 新增审核记录
export function addAuditRecord(data) {
  return request({
    url: '/web/auditRecord',
    method: 'post',
    data: data
  })
}

// 审批项目积分
export function auditProject2score(data) {
  return request({
    url: '/web/auditRecord/audit',
    method: 'post',
    data: data
  })
}

// 修改审核记录
export function updateAuditRecord(data) {
  return request({
    url: '/web/auditRecord',
    method: 'put',
    data: data
  })
}

// 删除审核记录
export function delAuditRecord(id) {
  return request({
    url: '/web/auditRecord/' + id,
    method: 'delete'
  })
}
