import request from '@/utils/request'

// 查询科研指标规则列表
export function listCompletionRule(query) {
  return request({
    url: '/web/completionRule/list',
    method: 'get',
    params: query
  })
}

// 查询科研指标规则详细
export function getCompletionRule(id) {
  return request({
    url: '/web/completionRule/' + id,
    method: 'get'
  })
}

// 新增科研指标规则
export function addCompletionRule(data) {
  return request({
    url: '/web/completionRule',
    method: 'post',
    data: data
  })
}

// 修改科研指标规则
export function updateCompletionRule(data) {
  return request({
    url: '/web/completionRule',
    method: 'put',
    data: data
  })
}

// 删除科研指标规则
export function delCompletionRule(id) {
  return request({
    url: '/web/completionRule/' + id,
    method: 'delete'
  })
}
