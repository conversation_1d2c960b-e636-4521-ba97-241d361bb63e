import request from '@/utils/request'

// 查询竞赛类别列表
export function listCategory(query) {
  return request({
    url: '/web/category/list',
    method: 'get',
    params: query
  })
}

// 查询竞赛类别详细
export function getCategory(id) {
  return request({
    url: '/web/category/' + id,
    method: 'get'
  })
}

// 新增竞赛类别
export function addCategory(data) {
  return request({
    url: '/web/category',
    method: 'post',
    data: data
  })
}

// 修改竞赛类别
export function updateCategory(data) {
  return request({
    url: '/web/category',
    method: 'put',
    data: data
  })
}

// 删除竞赛类别
export function delCategory(id) {
  return request({
    url: '/web/category/' + id,
    method: 'delete'
  })
}
