import request from '@/utils/request'

// 查询学生信息列表
export function listStudentInfo(query) {
  return request({
    url: '/web/studentInfo/list',
    method: 'get',
    params: query
  })
}

// 查询学生信息详细
export function getStudentInfo(id) {
  return request({
    url: '/web/studentInfo/' + id,
    method: 'get'
  })
}

// 新增学生信息
export function addStudentInfo(data) {
  return request({
    url: '/web/studentInfo',
    method: 'post',
    data: data
  })
}

// 修改学生信息
export function updateStudentInfo(data) {
  return request({
    url: '/web/studentInfo',
    method: 'put',
    data: data
  })
}

// 删除学生信息
export function delStudentInfo(id) {
  return request({
    url: '/web/studentInfo/' + id,
    method: 'delete'
  })
}
