import request from '@/utils/request'

// 获取个人积分统计数据
export function getPersonalScoreStats() {
  return request({
    url: '/web/score/personal/stats',
    method: 'get'
  })
}

// 获取个人积分趋势数据
export function getPersonalScoreTrend(params) {
  return request({
    url: '/web/score/personal/trend',
    method: 'get',
    params
  })
}

// 获取个人项目类型分布
export function getPersonalProjectTypes() {
  return request({
    url: '/web/score/personal/types',
    method: 'get'
  })
}

// 获取部门积分统计数据
export function getDepartmentScoreStats() {
  return request({
    url: '/web/score/department/stats',
    method: 'get'
  })
}

// 获取部门积分排名
export function getDepartmentScoreRanking() {
  return request({
    url: '/web/score/department/ranking',
    method: 'get'
  })
}

// 获取部门项目类型分布
export function getDepartmentProjectTypes() {
  return request({
    url: '/web/score/department/types',
    method: 'get'
  })
}

// 获取年度积分分析数据
export function getYearlyAnalysis(year) {
  return request({
    url: '/web/score/analysis/yearly',
    method: 'get',
    params: { year }
  })
}

// 获取积分趋势数据
export function getScoreTrend(year, timeUnit) {
  return request({
    url: '/web/score/analysis/trend',
    method: 'get',
    params: { year, timeUnit }
  })
}

// 获取项目类型分布数据
export function getTypeDistribution(year) {
  return request({
    url: '/web/score/analysis/type-distribution',
    method: 'get',
    params: { year }
  })
}

// 获取部门年度积分分析数据
export function getDeptYearlyAnalysis(deptId, year) {
  return request({
    url: '/web/score/analysis/dept/yearly',
    method: 'get',
    params: { deptId, year }
  })
}

// 获取部门积分趋势数据
export function getDeptScoreTrend(deptId, year, timeUnit) {
  return request({
    url: '/web/score/analysis/dept/trend',
    method: 'get',
    params: { deptId, year, timeUnit }
  })
}

// 获取部门人均积分趋势数据
export function getDeptAvgScoreTrend(deptId, year) {
  return request({
    url: '/web/score/analysis/dept/avg-trend',
    method: 'get',
    params: { deptId, year }
  })
}

// 获取部门排名趋势数据
export function getDeptRankTrend(deptId, year) {
  return request({
    url: '/web/score/analysis/dept/rank-trend',
    method: 'get',
    params: { deptId, year }
  })
}

// 获取部门项目类型分布数据
export function getDeptTypeDistribution(deptId, year) {
  return request({
    url: '/web/score/analysis/dept/type-distribution',
    method: 'get',
    params: { deptId, year }
  })
} 