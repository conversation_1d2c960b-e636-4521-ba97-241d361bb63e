import request from '@/utils/request'

// 查询竞赛选手列表
export function listCompContestant(query) {
  return request({
    url: '/web/compContestant/list',
    method: 'get',
    params: query
  })
}

// 查询竞赛选手详细
export function getCompContestant(id) {
  return request({
    url: '/web/compContestant/' + id,
    method: 'get'
  })
}

// 新增竞赛选手
export function addCompContestant(data) {
  return request({
    url: '/web/compContestant',
    method: 'post',
    data: data
  })
}

// 修改竞赛选手
export function updateCompContestant(data) {
  return request({
    url: '/web/compContestant',
    method: 'put',
    data: data
  })
}

// 删除竞赛选手
export function delCompContestant(id) {
  return request({
    url: '/web/compContestant/' + id,
    method: 'delete'
  })
}
