import request from '@/utils/request'

// 查询积分记录列表
export function listScoreRecord(query) {
  return request({
    url: '/web/scoreRecordExt/list',
    method: 'get',
    params: query
  })
}

// 查询积分记录列表管理员
export function listScoreRecordAdmin(query) {
  return request({
    url: '/web/scoreRecord/list',
    method: 'get',
    params: query
  })
}

// 查询积分记录详细
export function getScoreRecord(id) {
  return request({
    url: '/web/scoreRecordExt/' + id,
    method: 'get'
  })
}

// 查询积分记录详细员
export function getScoreRecordAdmin(id) {
  return request({
    url: '/web/scoreRecord/' + id,
    method: 'get'
  })
}


// 新增积分记录
export function addScoreRecord(data) {
  return request({
    url: '/web/scoreRecord',
    method: 'post',
    data: data
  })
}

// 修改积分记录
export function updateScoreRecord(data) {
  return request({
    url: '/web/scoreRecord',
    method: 'put',
    data: data
  })
}

// 删除积分记录
export function delScoreRecord(id) {
  return request({
    url: '/web/scoreRecord/' + id,
    method: 'delete'
  })
}

// 导出积分记录
export function exportScoreRecord(query) {
  return request({
    url: '/web/scoreRecordExt/export',
    method: 'post',
    params: query
  })
}
