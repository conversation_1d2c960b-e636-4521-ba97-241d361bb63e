import request from '@/utils/request'

// 查询积分分配列表
export function listDistribution(query) {
  return request({
    url: '/web/distribution/mylist',
    method: 'get',
    params: query
  })
}

// 查询积分分配详细
export function getDistribution(id) {
  return request({
    url: '/web/distribution/' + id,
    method: 'get'
  })
}

// 新增积分分配
export function addDistribution(data) {
  return request({
    url: '/web/distribution',
    method: 'post',
    data: data
  })
}

// 修改积分分配
export function updateDistribution(data) {
  return request({
    url: '/web/distribution',
    method: 'put',
    data: data
  })
}

// 删除积分分配
export function delDistribution(id) {
  return request({
    url: '/web/distribution/' + id,
    method: 'delete'
  })
}

// 批量保存团队成员分配
export function batchSaveDistribution(data) {
  return request({
    url: '/web/distribution/batchSave',
    method: 'post',
    data: data
  })
}

// 获取项目的分配情况
export function getProjectDistributions(projectId) {
  return request({
    url: '/web/distribution/project/' + projectId,
    method: 'get'
  })
}

// 批量保存项目积分分配
export function batchSaveDistributions(data) {
  return request({
    url: '/web/distribution/batchSave',
    method: 'post',
    data: data
  })
}

// 导出积分分配
export function exportDistribution(query) {
  return request({
    url: '/web/distribution/export',
    method: 'get',
    params: query
  })
}

