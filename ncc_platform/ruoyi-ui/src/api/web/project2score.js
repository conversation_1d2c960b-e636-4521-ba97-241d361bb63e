import request from '@/utils/request'

// 查询项目积分列表
export function listProject2score(query) {
  return request({
    url: '/web/project2score/list',
    method: 'get',
    params: query
  })
}

// 查询项目积分列表（按状态排序：待审核->已驳回->已通过->草稿）
export function listProject2scoreAudit(query) {
  return request({
    url: '/web/project2score/auditList',
    method: 'get',
    params: query
  })
}

// 查询当前用户的项目积分列表
export function listMyProject2score(query) {
  return request({
    url: '/web/project2score/myList',
    method: 'get',
    params: query
  })
}

// 查询项目积分详细
export function getProject2score(id) {
  return request({
    url: '/web/project2score/' + id,
    method: 'get'
  })
}

// 新增项目积分
export function addProject2score(data) {
  return request({
    url: '/web/project2score',
    method: 'post',
    data: data
  })
}

// 修改项目积分
export function updateProject2score(data) {
  return request({
    url: '/web/project2score',
    method: 'put',
    data: data
  })
}

// 删除项目积分
export function delProject2score(id) {
  return request({
    url: '/web/project2score/' + id,
    method: 'delete'
  })
}

// 提交项目积分审核
export function submitProject2score(id) {
  return request({
    url: '/web/project2score/submit/' + id,
    method: 'put'
  })
}

// 撤回审核
export function revokeAudit(projectId) {
  return request({
    url: '/web/project2score/revoke/' + projectId,
    method: 'put'
  })
}
