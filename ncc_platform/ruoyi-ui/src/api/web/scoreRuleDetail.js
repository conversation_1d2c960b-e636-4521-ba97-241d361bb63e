import request from '@/utils/request'

// 查询积分规则详情列表
export function listScoreRuleDetail(query) {
  return request({
    url: '/web/scoreRuleDetail/list',
    method: 'get',
    params: query
  })
}

// 查询积分规则详情详细
export function getScoreRuleDetail(id) {
  return request({
    url: '/web/scoreRuleDetail/' + id,
    method: 'get'
  })
}

// 新增积分规则详情
export function addScoreRuleDetail(data) {
  return request({
    url: '/web/scoreRuleDetail',
    method: 'post',
    data: data
  })
}

// 修改积分规则详情
export function updateScoreRuleDetail(data) {
  return request({
    url: '/web/scoreRuleDetail',
    method: 'put',
    data: data
  })
}

// 删除积分规则详情
export function delScoreRuleDetail(id) {
  return request({
    url: '/web/scoreRuleDetail/' + id,
    method: 'delete'
  })
}
