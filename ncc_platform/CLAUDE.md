# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

NCC Platform (南城院竞赛与科研积分管理平台) is a competition and research scoring management platform built on RuoYi-Vue framework. The platform manages academic competitions, research project scoring, participant management, and statistical analysis for educational institutions.

## Development Commands

### Frontend (ruoyi-ui/)
```bash
# Install dependencies
npm install

# Development server
npm run dev

# Production build
npm run build:prod

# Staging build  
npm run build:stage

# Lint code
npm run lint
```

### Backend
```bash
# Run backend using Maven
cd ruoyi-admin
mvn spring-boot:run

# Or use provided scripts
./ry.sh start|stop|restart|status  # Linux/Mac
ry.bat                            # Windows
```

### Database Setup
- Import SQL files from `sql/` directory in order:
  - `all_in_one.sql` (base schema)
  - `v2.sql`, `v2.1.sql` (version updates)
  - `quartz.sql` (scheduled tasks)

## Architecture Overview

### Multi-Module Maven Structure
- **ruoyi-admin**: Main application entry point and web controllers
- **ruoyi-framework**: Core framework components (security, config, etc.)
- **ruoyi-system**: System management modules (user, role, dept, etc.)
- **ruoyi-common**: Shared utilities and common components
- **ruoyi-generator**: Code generation tools
- **ruoyi-quartz**: Scheduled task management

### Frontend Architecture (Vue 2.6 + Element UI)
- **src/api/**: API service layer organized by business modules
- **src/views/web/**: Business-specific pages (competition, scoring, etc.)
- **src/components/**: Reusable Vue components
- **src/store/**: Vuex state management
- **src/router/**: Vue Router configuration
- **src/utils/**: Frontend utilities and helpers

### Key Business Modules
- **Competition Management**: Category, contestant, instructor, base info management
- **Project Scoring**: Rule-based scoring system with audit workflow
- **Score Analysis**: Statistical analysis and reporting
- **Student/Instructor Info**: Participant information management
- **Data Distribution**: Score distribution and completion tracking

### Database Layer
- **MyBatis Plus**: ORM framework for database operations
- **Druid**: Database connection pooling
- **MySQL**: Primary database
- **Redis**: Caching and session storage

## Configuration Files

### Backend Configuration
- `ruoyi-admin/src/main/resources/application.yml`: Main configuration
- `ruoyi-admin/src/main/resources/application-druid.yml`: Database configuration
- Database URL: `****************************************`
- Redis: `localhost:6379`

### Frontend Configuration
- `ruoyi-ui/vue.config.js`: Vue CLI configuration
- `ruoyi-ui/.env.*`: Environment-specific settings

## Development Workflow

### Adding New Business Modules
1. Create domain entities in `ruoyi-admin/src/main/java/com/ruoyi/web/domain/`
2. Create mapper interfaces in `ruoyi-admin/src/main/java/com/ruoyi/web/mapper/`
3. Create service interfaces and implementations in `ruoyi-admin/src/main/java/com/ruoyi/web/service/`
4. Create controllers in `ruoyi-admin/src/main/java/com/ruoyi/web/controller/`
5. Add corresponding XML mappers in `ruoyi-admin/src/main/resources/mapper/`
6. Create frontend API services in `ruoyi-ui/src/api/web/`
7. Create Vue components/pages in `ruoyi-ui/src/views/web/`

### Code Generation
Use the built-in code generator at `/tool/gen` (when running) to scaffold CRUD operations for new database tables.

### Authentication & Authorization
- JWT-based authentication with 30-minute expiration
- Role-based access control (RBAC)
- Permission annotations: `@PreAuthorize("@ss.hasPermi('web:module:action')")`

## Project-Specific Patterns

### API Response Format
All API responses follow AjaxResult format with `code`, `msg`, and `data` fields.

### Date Handling
Use `@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")` for consistent date serialization.

### File Upload
Upload path configured in `application.yml` as `ruoyi.profile` property (default: `D:/ncc_platform/uploadPath`).

### Validation
Use JSR-303 annotations on domain entities with custom validation groups for different operations.