package com.ruoyi.web.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.Project2scoreAuditRecordMapper;
import com.ruoyi.web.domain.Project2scoreAuditRecord;
import com.ruoyi.web.service.IProject2scoreAuditRecordService;
import com.ruoyi.web.mapper.Project2scoreMapper;
import com.ruoyi.web.domain.Project2score;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.web.domain.Project2scoreDistribution;
import com.ruoyi.web.mapper.Project2scoreDistributionMapper;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.mapper.Project2scoreRecordMapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.mapper.SysUserMapper;

/**
 * 审核记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class Project2scoreAuditRecordServiceImpl implements IProject2scoreAuditRecordService 
{
    @Autowired
    private Project2scoreAuditRecordMapper project2scoreAuditRecordMapper;

    @Autowired
    private Project2scoreMapper project2scoreMapper;

    @Autowired
    private Project2scoreDistributionMapper project2scoreDistributionMapper;

    @Autowired
    private Project2scoreRecordMapper project2scoreRecordMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询审核记录
     * 
     * @param id 审核记录主键
     * @return 审核记录
     */
    @Override
    public Project2scoreAuditRecord selectProject2scoreAuditRecordById(Long id)
    {
        return project2scoreAuditRecordMapper.selectProject2scoreAuditRecordById(id);
    }

    /**
     * 查询审核记录列表
     * 
     * @param project2scoreAuditRecord 审核记录
     * @return 审核记录
     */
    @Override
    public List<Project2scoreAuditRecord> selectProject2scoreAuditRecordList(Project2scoreAuditRecord project2scoreAuditRecord)
    {
        return project2scoreAuditRecordMapper.selectProject2scoreAuditRecordList(project2scoreAuditRecord);
    }

    /**
     * 新增审核记录
     * 
     * @param project2scoreAuditRecord 审核记录
     * @return 结果
     */
    @Override
    public int insertProject2scoreAuditRecord(Project2scoreAuditRecord project2scoreAuditRecord)
    {
        project2scoreAuditRecord.setCreateTime(DateUtils.getNowDate());
        return project2scoreAuditRecordMapper.insertProject2scoreAuditRecord(project2scoreAuditRecord);
    }

    /**
     * 修改审核记录
     * 
     * @param project2scoreAuditRecord 审核记录
     * @return 结果
     */
    @Override
    public int updateProject2scoreAuditRecord(Project2scoreAuditRecord project2scoreAuditRecord)
    {
        return project2scoreAuditRecordMapper.updateProject2scoreAuditRecord(project2scoreAuditRecord);
    }

    /**
     * 批量删除审核记录
     * 
     * @param ids 需要删除的审核记录主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreAuditRecordByIds(Long[] ids)
    {
        return project2scoreAuditRecordMapper.deleteProject2scoreAuditRecordByIds(ids);
    }

    /**
     * 删除审核记录信息
     * 
     * @param id 审核记录主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreAuditRecordById(Long id)
    {
        return project2scoreAuditRecordMapper.deleteProject2scoreAuditRecordById(id);
    }

    /**
     * 审批项目积分
     * 
     * @param auditRecord 审核记录
     * @return 结果
     */
    @Override
    @Transactional
    public int auditProject2score(Project2scoreAuditRecord auditRecord) {
        try {
            // 1. 获取项目信息
            Project2score project = project2scoreMapper.selectProject2scoreById(auditRecord.getProjectId());
            if (project == null) {
                throw new RuntimeException("项目不存在");
            }

            // 2. 保存审核记录
            int rows = project2scoreAuditRecordMapper.insertProject2scoreAuditRecord(auditRecord);

            // 3. 更新项目状态
            String newStatus = "0".equals(auditRecord.getAuditStatus()) ? "2" : "3"; // 0-通过，1-驳回
            project.setStatus(newStatus);
            project.setUpdateTime(DateUtils.getNowDate());
            project.setUpdateBy(auditRecord.getCreateBy());
            project2scoreMapper.updateProject2score(project);

            // 4. 如果审核通过，记录积分
            if ("0".equals(auditRecord.getAuditStatus())) {
                if ("1".equals(project.getIsTeam())) {
                    // 团队项目：根据分配情况记录积分
                    Project2scoreDistribution query = new Project2scoreDistribution();
                    query.setProjectId(project.getId());
                    List<Project2scoreDistribution> distributions = project2scoreDistributionMapper.selectProject2scoreDistributionList(query);
                    
                    for (Project2scoreDistribution distribution : distributions) {
                        // 如果是外部用户（userId为0），则跳过积分记录
                        if (distribution.getUserId() == 0) {
                            continue;
                        }
                        
                        // 获取用户信息以获取部门ID
                        SysUser user = sysUserMapper.selectUserById(distribution.getUserId());
                        if (user == null) {
                            throw new RuntimeException("用户不存在：" + distribution.getUserId());
                        }

                        Project2scoreRecord record = new Project2scoreRecord();
                        record.setProjectId(project.getId());
                        record.setUserId(distribution.getUserId());
                        record.setDeptId(user.getDeptId());
                        record.setScore(distribution.getScore());
                        record.setRuleId(project.getRuleId());
                        record.setType("0");
                        record.setProjectTime(project.getProjectTime());
                        record.setCreateTime(DateUtils.getNowDate());
                        record.setCreateBy(auditRecord.getCreateBy());

                        // 构建详细的备注信息
                        StringBuilder remarkBuilder = new StringBuilder();
                        remarkBuilder.append("来自项目「").append(project.getApplyName()).append("」的积分");
                        remarkBuilder.append("，在团队合作中担任");
                        remarkBuilder.append("1".equals(distribution.getIsLeader()) ? "负责人" : "团队成员");
                        remarkBuilder.append("，分配比例").append(distribution.getRatio()).append("%");
                        
                        // 如果原项目有备注，则添加到末尾
                        if (project.getRemark() != null && !project.getRemark().trim().isEmpty()) {
                            remarkBuilder.append("。备注：").append(project.getRemark());
                        }
                        
                        record.setRemark(remarkBuilder.toString());
                        project2scoreRecordMapper.insertProject2scoreRecord(record);
                    }
                } else {
                    // 非团队项目：所有积分记录给项目创建人
                    SysUser user = sysUserMapper.selectUserById(project.getUserId());
                    if (user == null) {
                        throw new RuntimeException("用户不存在：" + project.getUserId());
                    }

                    Project2scoreRecord record = new Project2scoreRecord();
                    record.setProjectId(project.getId());
                    record.setUserId(project.getUserId());
                    record.setDeptId(user.getDeptId());
                    record.setScore(project.getScore());
                    record.setRuleId(project.getRuleId());
                    record.setType("0");
                    record.setProjectTime(project.getProjectTime());
                    record.setCreateTime(DateUtils.getNowDate());
                    record.setCreateBy(auditRecord.getCreateBy());

                    // 构建详细的备注信息
                    StringBuilder remarkBuilder = new StringBuilder();
                    remarkBuilder.append("来自项目「").append(project.getApplyName()).append("」的积分");
                    
                    // 如果原项目有备注，则添加到末尾
                    if (project.getRemark() != null && !project.getRemark().trim().isEmpty()) {
                        remarkBuilder.append("。备注：").append(project.getRemark());
                    }
                    
                    record.setRemark(remarkBuilder.toString());
                    project2scoreRecordMapper.insertProject2scoreRecord(record);
                }
            }

            return rows;
        } catch (Exception e) {
            throw new RuntimeException("审核失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近一次审核记录
     * 
     * @param projectId 项目ID
     * @return 审核记录
     */
    @Override
    public Project2scoreAuditRecord selectLatestAuditRecord(Long projectId) {
        return project2scoreAuditRecordMapper.selectLatestAuditRecord(projectId);
    }
}
