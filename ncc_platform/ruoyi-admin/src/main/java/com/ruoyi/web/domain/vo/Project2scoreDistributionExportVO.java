package com.ruoyi.web.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 积分分配导出对象
 */
public class Project2scoreDistributionExportVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 是否为负责人 */
    @Excel(name = "是否为负责人", readConverterExp = "0=否,1=是")
    private String isLeader;

    /** 团队成员 */
    @Excel(name = "团队成员")
    private String teamMembers;

    /** 项目总积分 */
    @Excel(name = "项目总积分")
    private String totalScore;

    /** 积分分配详情 */
    @Excel(name = "积分分配详情")
    private String scoreDistribution;

    /** 项目状态 */
    @Excel(name = "项目状态", readConverterExp = "0=草稿,1=待审核,2=已通过,3=已驳回")
    private String status;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getIsLeader() {
        return isLeader;
    }

    public void setIsLeader(String isLeader) {
        this.isLeader = isLeader;
    }

    public String getTeamMembers() {
        return teamMembers;
    }

    public void setTeamMembers(String teamMembers) {
        this.teamMembers = teamMembers;
    }

    public String getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(String totalScore) {
        this.totalScore = totalScore;
    }

    public String getScoreDistribution() {
        return scoreDistribution;
    }

    public void setScoreDistribution(String scoreDistribution) {
        this.scoreDistribution = scoreDistribution;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}