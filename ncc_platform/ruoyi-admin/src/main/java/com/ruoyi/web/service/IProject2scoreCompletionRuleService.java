package com.ruoyi.web.service;

import java.util.List;
import com.ruoyi.web.domain.Project2scoreCompletionRule;

/**
 * 科研指标规则Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IProject2scoreCompletionRuleService 
{
    /**
     * 查询科研指标规则
     * 
     * @param id 科研指标规则主键
     * @return 科研指标规则
     */
    public Project2scoreCompletionRule selectProject2scoreCompletionRuleById(Long id);

    /**
     * 查询科研指标规则列表
     * 
     * @param project2scoreCompletionRule 科研指标规则
     * @return 科研指标规则集合
     */
    public List<Project2scoreCompletionRule> selectProject2scoreCompletionRuleList(Project2scoreCompletionRule project2scoreCompletionRule);

    /**
     * 新增科研指标规则
     * 
     * @param project2scoreCompletionRule 科研指标规则
     * @return 结果
     */
    public int insertProject2scoreCompletionRule(Project2scoreCompletionRule project2scoreCompletionRule);

    /**
     * 修改科研指标规则
     * 
     * @param project2scoreCompletionRule 科研指标规则
     * @return 结果
     */
    public int updateProject2scoreCompletionRule(Project2scoreCompletionRule project2scoreCompletionRule);

    /**
     * 批量删除科研指标规则
     * 
     * @param ids 需要删除的科研指标规则主键集合
     * @return 结果
     */
    public int deleteProject2scoreCompletionRuleByIds(Long[] ids);

    /**
     * 删除科研指标规则信息
     * 
     * @param id 科研指标规则主键
     * @return 结果
     */
    public int deleteProject2scoreCompletionRuleById(Long id);
}
