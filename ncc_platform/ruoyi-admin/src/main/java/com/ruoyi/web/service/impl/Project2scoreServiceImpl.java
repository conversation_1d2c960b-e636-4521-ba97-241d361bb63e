package com.ruoyi.web.service.impl;

import java.util.List;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.web.domain.Project2scoreDistribution;
import com.ruoyi.web.domain.Project2scoreAuditRecord;
import com.ruoyi.web.domain.Project2scoreRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.Project2scoreMapper;
import com.ruoyi.web.domain.Project2score;
import com.ruoyi.web.service.IProject2scoreService;
import com.ruoyi.web.service.IProject2scoreDistributionService;
import com.ruoyi.web.service.IProject2scoreAuditRecordService;
import com.ruoyi.web.service.IProject2scoreRecordService;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.web.domain.vo.Project2scoreExportVO;
import com.ruoyi.web.domain.vo.Project2scoreAuditExportVO;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.mapper.SysDeptMapper;
import java.util.ArrayList;

/**
 * 项目积分Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class Project2scoreServiceImpl implements IProject2scoreService 
{
    @Autowired
    private Project2scoreMapper project2scoreMapper;

    @Autowired
    private IProject2scoreDistributionService distributionService;

    @Autowired
    private IProject2scoreAuditRecordService auditRecordService;

    @Autowired
    private IProject2scoreRecordService recordService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询项目积分
     * 
     * @param id 项目积分主键
     * @return 项目积分
     */
    @Override
    public Project2score selectProject2scoreById(Long id)
    {
        return project2scoreMapper.selectProject2scoreById(id);
    }

    /**
     * 查询项目积分列表
     * 
     * @param project2score 项目积分
     * @return 项目积分
     */
    @Override
    public List<Project2score> selectProject2scoreList(Project2score project2score)
    {
        return project2scoreMapper.selectProject2scoreList(project2score);
    }

    /**
     * 查询当前用户的项目积分列表（按状态排序）
     * 
     * @param project2score 项目积分
     * @return 项目积分
     */
    @Override
    public List<Project2score> selectMyProject2scoreList(Project2score project2score)
    {
        return project2scoreMapper.selectMyProject2scoreList(project2score);
    }

    /**
     * 查询项目积分列表（按状态排序：待审核->已驳回->已通过->草稿）
     * 
     * @param project2score 项目积分
     * @return 项目积分
     */
    @Override
    public List<Project2score> selectProject2scoreAuditList(Project2score project2score)
    {
        // 获取当前登录用户
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 管理员可查看所有数据
        if (user.isAdmin())
        {
            return project2scoreMapper.selectProject2scoreAuditList(project2score);
        }
        
        // 获取用户的部门信息
        SysDept dept = deptService.selectDeptById(user.getDeptId());
        if (dept != null)
        {
            // 解析部门的ancestors确定部门层级
            String ancestors = dept.getAncestors();
            // 获取一级部门ID
            Long firstLevelDeptId = null;
            // 从ancestors判断当前用户所在部门的层级
            // 顶级部门的ancestors为空
            // 一级部门的ancestors为"0"
            // 二级部门的ancestors为"0,一级部门ID"
            // 三级部门的ancestors为"0,一级部门ID,二级部门ID"
            String[] ancestorArray = ancestors.split(",");
            if (ancestorArray.length == 2)
            {
                // 用户在二级部门，直接使用当前部门ID
                firstLevelDeptId = dept.getDeptId();
            } 
            else if (ancestorArray.length > 2)
            {
                // 用户在二级或更深层级部门，获取一级部门ID
                // "0"是索引0，所以一级部门ID是索引1
                    firstLevelDeptId = Long.valueOf(ancestorArray[2]);
            }
            
            if (firstLevelDeptId != null)
            {
                // 获取二级部门及其所有子部门
                List<SysDept> childDepts = sysDeptMapper.selectChildrenDeptById(firstLevelDeptId);
                List<Long> deptIds = new ArrayList<>();
                deptIds.add(firstLevelDeptId); // 添加一级部门自身
                // 添加所有子部门ID
                for (SysDept childDept : childDepts)
                {
                    deptIds.add(childDept.getDeptId());
                }
                // 设置部门过滤条件
                project2score.setDeptIds(deptIds);
            }
        }
        
        // 查询结果
        return project2scoreMapper.selectProject2scoreAuditList(project2score);
    }

    /**
     * 查询项目积分导出列表
     * 
     * @param project2score 项目积分
     * @return 项目积分导出列表
     */
    @Override
    public List<Project2scoreExportVO> selectProject2scoreExportList(Project2score project2score)
    {
        return project2scoreMapper.selectProject2scoreExportList(project2score);
    }

    /**
     * 查询项目积分审核导出列表
     * 
     * @param project2score 项目积分信息
     * @return 项目积分审核导出集合
     */
    @Override
    public List<Project2scoreAuditExportVO> selectProject2scoreAuditExportList(Project2score project2score)
    {
        return project2scoreMapper.selectProject2scoreAuditExportList(project2score);
    }

    /**
     * 新增项目积分
     * 
     * @param project2score 项目积分
     * @return 结果
     */
    @Override
    public int insertProject2score(Project2score project2score)
    {
        project2score.setCreateTime(DateUtils.getNowDate());
        return project2scoreMapper.insertProject2score(project2score);
    }

    /**
     * 修改项目积分
     * 
     * @param project2score 项目积分
     * @return 结果
     */
    @Override
    public int updateProject2score(Project2score project2score)
    {
        project2score.setUpdateTime(DateUtils.getNowDate());
        return project2scoreMapper.updateProject2score(project2score);
    }

    /**
     * 批量删除项目积分
     * 
     * @param ids 需要删除的项目积分主键集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteProject2scoreByIds(Long[] ids)
    {
        int rows = 0;
        try {
            for (Long id : ids) {
                // 1. 删除团队成员分配记录（project2score_distribution表）
                Project2scoreDistribution distribution = new Project2scoreDistribution();
                distribution.setProjectId(id);
                List<Project2scoreDistribution> distributions = distributionService.selectProject2scoreDistributionList(distribution);
                if (!distributions.isEmpty()) {
                    Long[] distributionIds = distributions.stream()
                            .map(Project2scoreDistribution::getId)
                            .toArray(Long[]::new);
                    distributionService.deleteProject2scoreDistributionByIds(distributionIds);
                }

                // 2. 删除审核记录（project2score_audit_record表）
                Project2scoreAuditRecord auditRecord = new Project2scoreAuditRecord();
                auditRecord.setProjectId(id);
                List<Project2scoreAuditRecord> auditRecords = auditRecordService.selectProject2scoreAuditRecordList(auditRecord);
                if (!auditRecords.isEmpty()) {
                    Long[] auditRecordIds = auditRecords.stream()
                            .map(Project2scoreAuditRecord::getId)
                            .toArray(Long[]::new);
                    auditRecordService.deleteProject2scoreAuditRecordByIds(auditRecordIds);
                }

                // 3. 删除积分记录（project2score_record表）
                Project2scoreRecord record = new Project2scoreRecord();
                record.setProjectId(id);
                List<Project2scoreRecord> records = recordService.selectProject2scoreRecordList(record);
                if (!records.isEmpty()) {
                    Long[] recordIds = records.stream()
                            .map(Project2scoreRecord::getId)
                            .toArray(Long[]::new);
                    recordService.deleteProject2scoreRecordByIds(recordIds);
                }

                // 4. 最后删除项目积分主记录（project2score表）
                rows += project2scoreMapper.deleteProject2scoreById(id);
            }
            return rows;
        } catch (Exception e) {
            throw new RuntimeException("删除项目积分失败，请检查是否存在关联数据: " + e.getMessage());
        }
    }

    /**
     * 删除项目积分信息
     * 
     * @param id 项目积分主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreById(Long id)
    {
        return project2scoreMapper.deleteProject2scoreById(id);
    }

    /**
     * 新增项目积分(包含团队成员)
     *
     * @param project
     * @param distributions
     */

    @Override
    @Transactional
    public Long insertProject2scoreWithTeam(Project2score project, List<Project2scoreDistribution> distributions) {
        try {
            // 1. 保存项目
            System.out.println("项目信息：" + project.toString());
            project.setCreateTime(DateUtils.getNowDate());
            project2scoreMapper.insertProject2score(project);
            Long projectId = project.getId();
            System.out.println("项目ID：" + projectId);
            // 2. 如果是团队项目，保存团队成员分配
            if ("1".equals(project.getIsTeam()) && distributions != null && !distributions.isEmpty()) {
                for (Project2scoreDistribution distribution : distributions) {
                    // 设置基本信息
                    distribution.setProjectId(projectId);
                    distribution.setCreateTime(DateUtils.getNowDate());
                    distribution.setCreateBy(project.getCreateBy());
                }
                distributionService.batchSaveDistributions(distributions);
            }

            return projectId;
        } catch (Exception e) {
            throw new RuntimeException("保存项目失败: " + e.getMessage());
        }
    }

    /**
     * 修改项目积分(包含团队成员)
     */
    @Override
    @Transactional
    public int updateProject2scoreWithTeam(Project2score project, List<Project2scoreDistribution> distributions) {
        try {
            // 1. 更新项目基本信息
            project.setUpdateTime(DateUtils.getNowDate());
            int rows = project2scoreMapper.updateProject2score(project);

            // 2. 处理团队成员
            if ("1".equals(project.getIsTeam())) {
                if (distributions != null && !distributions.isEmpty()) {
                    // 2.1 先删除原有的所有团队成员
                    distributionService.deleteByProjectId(project.getId());
                    
                    // 2.2 批量新增团队成员，保留创建信息同时更新修改信息
                    Date now = DateUtils.getNowDate();
                    for (Project2scoreDistribution distribution : distributions) {
                        // 设置项目ID
                        distribution.setProjectId(project.getId());
                        
                        // 设置创建信息（如果是新成员）
                        if (distribution.getCreateBy() == null) {
                            distribution.setCreateTime(now);
                            distribution.setCreateBy(project.getUpdateBy());
                        }
                        
                        // 设置更新信息
                        distribution.setUpdateTime(now);
                        distribution.setUpdateBy(project.getUpdateBy());
                    }
                    
                    // 2.3 批量保存团队成员
                    distributionService.batchSaveDistributions(distributions);
                }
            } else {
                // 如果不是团队项目，删除所有团队成员
                distributionService.deleteByProjectId(project.getId());
            }

            return rows;
        } catch (Exception e) {
            throw new RuntimeException("更新项目失败: " + e.getMessage());
        }
    }

    /**
     * 撤回项目积分审核
     * 
     * @param id 项目积分ID
     * @param userId 当前用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int revokeProject2score(Long id, Long userId) {
        // 1. 获取项目信息
        Project2score project = project2scoreMapper.selectProject2scoreById(id);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }

        // 2. 获取最近一次审核记录
        Project2scoreAuditRecord latestAudit = auditRecordService.selectLatestAuditRecord(id);
        if (latestAudit == null) {
            throw new RuntimeException("未找到审核记录");
        }

        // 3. 验证是否是最近一次审核人
        if (!userId.equals(latestAudit.getAuditUserId())) {
            throw new RuntimeException("只有最近一次审核人可以撤回");
        }

        // 4. 如果是已通过状态，删除积分记录
        if ("2".equals(project.getStatus())) {
            // 删除项目相关的所有积分记录
            Project2scoreRecord record = new Project2scoreRecord();
            record.setProjectId(id);
            recordService.deleteProject2scoreRecordByProjectId(id);
        }

        // 5. 删除最近一次审核记录
        auditRecordService.deleteProject2scoreAuditRecordById(latestAudit.getId());

        // 6. 更新项目状态为待审核
        project.setStatus("1"); // 待审核状态
        project.setUpdateTime(DateUtils.getNowDate());
        project.setUpdateBy(SecurityUtils.getUsername());

        return project2scoreMapper.updateProject2score(project);
    }
}
