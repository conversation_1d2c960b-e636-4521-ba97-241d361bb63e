package com.ruoyi.web.domain.vo;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class ScoreAnalysisVO {
    /** 年度总积分 */
    private BigDecimal totalScore;
    
    /** 积分增长率 */
    private BigDecimal scoreGrowth;
    
    /** 校内排名 */
    private Long schoolRank;
    
    /** 总用户数 */
    private Long totalUsers;
    
    /** 排名变化 */
    private Long rankChange;
    
    /** 部门排名 */
    private Long deptRank;
    
    /** 部门总数 */
    private Long deptTotal;
    
    /** 部门排名变化 */
    private Long deptRankChange;
    
    /** 项目数量 */
    private Long projectCount;
    
    /** 项目数量增长率 */
    private BigDecimal projectGrowth;
} 