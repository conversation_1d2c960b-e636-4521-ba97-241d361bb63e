package com.ruoyi.web.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.domain.CompInstructor;
import org.apache.ibatis.annotations.Mapper;

/**
 * 竞赛指导教师Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Mapper
public interface CompInstructorMapper extends BaseMapper<CompInstructor>
{
    /**
     * 查询竞赛指导教师
     * 
     * @param id 竞赛指导教师主键
     * @return 竞赛指导教师
     */
    public CompInstructor selectCompInstructorById(Long id);

    /**
     * 查询竞赛指导教师列表
     * 
     * @param compInstructor 竞赛指导教师
     * @return 竞赛指导教师集合
     */
    public List<CompInstructor> selectCompInstructorList(CompInstructor compInstructor);

    /**
     * 新增竞赛指导教师
     * 
     * @param compInstructor 竞赛指导教师
     * @return 结果
     */
    public int insertCompInstructor(CompInstructor compInstructor);

    /**
     * 修改竞赛指导教师
     * 
     * @param compInstructor 竞赛指导教师
     * @return 结果
     */
    public int updateCompInstructor(CompInstructor compInstructor);

    /**
     * 删除竞赛指导教师
     * 
     * @param id 竞赛指导教师主键
     * @return 结果
     */
    public int deleteCompInstructorById(Long id);

    /**
     * 批量删除竞赛指导教师
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompInstructorByIds(Long[] ids);
}
