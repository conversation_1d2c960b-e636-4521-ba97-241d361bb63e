package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 竞赛指导教师对象 comp_instructor
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CompInstructorVO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指导教师 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 教师ID */
    @Excel(name = "教师ID")
    private String teacherId;

    /** 教研室ID */
    @Excel(name = "教研室ID")
    private Long trOId;

    /** 比赛ID */
    @Excel(name = "比赛ID")
    private Long compId;

    private String name;
    private String deptName;
    private String no;
    private String phone;

}
