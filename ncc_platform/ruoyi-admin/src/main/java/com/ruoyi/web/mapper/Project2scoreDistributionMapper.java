package com.ruoyi.web.mapper;

import java.util.List;
import com.ruoyi.web.domain.Project2scoreDistribution;
import com.ruoyi.web.domain.vo.Project2scoreDistributionVO;
import com.ruoyi.web.domain.vo.Project2scoreDistributionExportVO;

/**
 * 积分分配Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface Project2scoreDistributionMapper 
{
    /**
     * 查询积分分配
     * 
     * @param id 积分分配主键
     * @return 积分分配
     */
    public Project2scoreDistribution selectProject2scoreDistributionById(Long id);

    /**
     * 查询积分分配列表
     * 
     * @param project2scoreDistribution 积分分配
     * @return 积分分配集合
     */
    public List<Project2scoreDistribution> selectProject2scoreDistributionList(Project2scoreDistribution project2scoreDistribution);

    /**
     * 查询当前用户参与的团队项目积分分配列表
     * 
     * @param project2scoreDistribution 积分分配
     * @return 积分分配集合
     */
    public List<Project2scoreDistributionVO> selectMyDistributionList(Project2scoreDistribution project2scoreDistribution);

    /**
     * 新增积分分配
     * 
     * @param project2scoreDistribution 积分分配
     * @return 结果
     */
    public int insertProject2scoreDistribution(Project2scoreDistribution project2scoreDistribution);

    /**
     * 修改积分分配
     * 
     * @param project2scoreDistribution 积分分配
     * @return 结果
     */
    public int updateProject2scoreDistribution(Project2scoreDistribution project2scoreDistribution);

    /**
     * 删除积分分配
     * 
     * @param id 积分分配主键
     * @return 结果
     */
    public int deleteProject2scoreDistributionById(Long id);

    /**
     * 批量删除积分分配
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProject2scoreDistributionByIds(Long[] ids);

    /**
     * 删除项目的所有团队成员
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    public int deleteByProjectId(Long projectId);

    /**
     * 批量插入团队成员分配
     * 
     * @param distributions 团队成员分配列表
     * @return 结果
     */
    public int batchInsertDistributions(List<Project2scoreDistribution> distributions);

    /**
     * 根据项目ID查询团队成员分配记录
     * 
     * @param projectId 项目ID
     * @return 团队成员分配记录列表
     */
    public List<Project2scoreDistribution> selectProject2scoreDistributionByProjectId(Long projectId);

    /**
     * 批量新增团队成员分配记录
     * 
     * @param members 团队成员列表
     * @return 结果
     */
    public int batchInsert(List<Project2scoreDistribution> members);

    /**
     * 查询导出数据列表
     * 
     * @param project2scoreDistribution 查询条件
     * @return 导出数据列表
     */
    public List<Project2scoreDistributionExportVO> selectDistributionExportList(Project2scoreDistributionVO project2scoreDistribution);
}
