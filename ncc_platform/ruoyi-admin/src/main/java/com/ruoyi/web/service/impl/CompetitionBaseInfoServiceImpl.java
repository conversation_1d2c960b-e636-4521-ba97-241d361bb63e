package com.ruoyi.web.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.web.domain.*;
import com.ruoyi.web.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.service.ICompetitionBaseInfoService;

/**
 * 竞赛信息基础信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public class CompetitionBaseInfoServiceImpl implements ICompetitionBaseInfoService 
{
    @Autowired
    private CompetitionBaseInfoMapper competitionBaseInfoMapper;

    @Autowired
    private CompInstructorMapper compInstructorMapper;

    @Autowired
    private CompContestantMapper compContestantMapper;
    @Autowired
    private JiexiangApplicationMapper jiexiangApplicationMapper;
    @Autowired
    private LixiangApplicationMapper lixiangApplicationMapper;

    /**
     * 查询竞赛信息基础信息
     * 
     * @param id 竞赛信息基础信息主键
     * @return 竞赛信息基础信息
     */
    @Override
    public CompetitionBaseInfo selectCompetitionBaseInfoById(Long id)
    {
        return competitionBaseInfoMapper.selectCompetitionBaseInfoById(id);
    }

    /**
     * 查询竞赛信息基础信息列表
     * 
     * @param competitionBaseInfo 竞赛信息基础信息
     * @return 竞赛信息基础信息
     */
    @Override
    public List<CompetitionBaseInfo> selectCompetitionBaseInfoList(CompetitionBaseInfo competitionBaseInfo, LoginUser user)
    {
        //如何判断查询范围：查询角色 -> 查询岗位 如果是：
        SysUser userInfo = user.getUser();
        SysDept dept = userInfo.getDept();//获取部门信息
        List<SysRole> roles = userInfo.getRoles();
        if(roles.get(0).getRoleName().equals("教师")||roles.get(0).getRoleName().equals("专业负责人")){
            //如果是教师和专业负责人（查询自己）
            competitionBaseInfo.setCreateUserId(userInfo.getUserId());
        }else if(roles.get(0).getRoleName().equals("教研室主任")){
            competitionBaseInfo.setCreateUserDept(dept.getDeptId());
        }
        return competitionBaseInfoMapper.selectCompetitionBaseInfoList(competitionBaseInfo);
    }

    /**
     * 新增竞赛信息基础信息
     * 
     * @param competitionBaseInfo 竞赛信息基础信息
     * @return 结果
     */
    @Override
    public int insertCompetitionBaseInfo(CompetitionBaseInfo competitionBaseInfo)
    {
        competitionBaseInfo.setCreateTime(DateUtils.getNowDate());
        competitionBaseInfo.setStatus(0);
        return competitionBaseInfoMapper.insertCompetitionBaseInfo(competitionBaseInfo);
    }

    /**
     * 修改竞赛信息基础信息
     * 
     * @param competitionBaseInfo 竞赛信息基础信息
     * @return 结果
     */
    @Override
    public int updateCompetitionBaseInfo(CompetitionBaseInfo competitionBaseInfo)
    {
        if (competitionBaseInfo.getStatus() != null&&competitionBaseInfo.getStatus() == 2) {//竞赛立项
            competitionBaseInfo.setApprovalTime(DateUtils.getNowDate());
        }
        if (competitionBaseInfo.getStatus() != null&&competitionBaseInfo.getStatus() == 4) {//竞赛结项
            competitionBaseInfo.setCloseTime(DateUtils.getNowDate());
        }
        competitionBaseInfo.setUpdateTime(DateUtils.getNowDate());
        return competitionBaseInfoMapper.updateCompetitionBaseInfo(competitionBaseInfo);
    }

    /**
     * 批量删除竞赛信息基础信息
     * 
     * @param ids 需要删除的竞赛信息基础信息主键
     * @return 结果
     */
    @Override
    public int deleteCompetitionBaseInfoByIds(Long[] ids)
    {
        //判断ids[0]是否有数据
        if(ids[0] == null){
            throw new RuntimeException("非法删除");
        }
        jiexiangApplicationMapper.delete(new QueryWrapper<JiexiangApplication>().in("competition_id",ids[0]));
        lixiangApplicationMapper.delete(new QueryWrapper<LixiangApplication>().in("competition_id",ids[0]));
        compContestantMapper.delete(new QueryWrapper<CompContestant>().in("competition_id",ids[0]));
        compInstructorMapper.delete(new QueryWrapper<CompInstructor>().in("comp_id",ids[0]));
        return competitionBaseInfoMapper.deleteCompetitionBaseInfoByIds(ids);
    }

    /**
     * 删除竞赛信息基础信息信息
     * 
     * @param id 竞赛信息基础信息主键
     * @return 结果
     */
    @Override
    public int deleteCompetitionBaseInfoById(Long id)
    {
        return competitionBaseInfoMapper.deleteCompetitionBaseInfoById(id);
    }
}
