package com.ruoyi.web.mapper;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.ScoreCompletionVO;

/**
 * 积分完成情况Mapper接口
 * 
 * <AUTHOR>
 */
public interface ScoreCompletionMapper
{
    /**
     * 查询积分完成情况列表
     * 
     * @param params 查询参数
     * @return 积分完成情况集合
     */
    public List<ScoreCompletionVO> selectScoreCompletionList(Map<String, Object> params);

    /**
     * 查询用户积分明细列表
     * 
     * @param userId 用户ID
     * @param params 查询参数
     * @return 积分记录集合
     */
    public List<Project2scoreRecord> selectScoreDetailsList(Long userId, Map<String, Object> params);

    /**
     * 统计用户积分明细总数
     * 
     * @param userId 用户ID
     * @param params 查询参数
     * @return 总记录数
     */
    public int countScoreDetails(Long userId, Map<String, Object> params);
} 