package com.ruoyi.web.service;

import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.Project2scoreRecordVO;
import com.ruoyi.web.domain.vo.Project2scoreRecordExportVO;

import java.util.List;

/**
 * 积分记录扩展Service接口
 * 
 * <AUTHOR>
 */
public interface IProject2scoreRecordExtService {
    /**
     * 查询积分记录列表（关联用户、部门、项目、规则信息）
     *
     * @param record 积分记录
     * @return 积分记录集合
     */
    List<Project2scoreRecordVO> selectProject2scoreRecordVOList(Project2scoreRecord record);

    /**
     * 获取积分记录详细信息
     * 
     * @param id 积分记录主键
     * @return 积分记录
     */
    Project2scoreRecordVO selectProject2scoreRecordVOById(Long id);

    /**
     * 导出积分记录列表
     */
    List<Project2scoreRecordExportVO> selectExportList(Project2scoreRecord record);
} 