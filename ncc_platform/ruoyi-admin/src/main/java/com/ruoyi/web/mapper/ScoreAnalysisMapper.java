package com.ruoyi.web.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.web.domain.vo.ScoreAnalysisVO;
import com.ruoyi.web.domain.vo.ScoreTrendVO;
import com.ruoyi.web.domain.vo.ScoreTypeDistributionVO;
import com.ruoyi.web.domain.vo.DeptScoreAnalysisVO;

public interface ScoreAnalysisMapper {
    /**
     * 获取用户年度积分统计
     */
    ScoreAnalysisVO selectUserYearlyStats(@Param("userId") Long userId, @Param("year") Integer year);
    
    /**
     * 获取用户上一年度积分统计（用于计算同比）
     */
    ScoreAnalysisVO selectUserLastYearStats(@Param("userId") Long userId, @Param("year") Integer year);
    
    /**
     * 获取用户月度积分趋势
     */
    List<ScoreTrendVO> selectUserMonthlyTrend(@Param("userId") Long userId, @Param("year") Integer year);
    
    /**
     * 获取用户季度积分趋势
     */
    List<ScoreTrendVO> selectUserQuarterlyTrend(@Param("userId") Long userId, @Param("year") Integer year);
    
    /**
     * 获取用户项目类型分布
     */
    List<ScoreTypeDistributionVO> selectUserProjectTypeDistribution(@Param("userId") Long userId, @Param("year") Integer year);

    /**
     * 获取部门年度积分统计
     */
    DeptScoreAnalysisVO selectDeptYearlyStats(@Param("deptId") Long deptId, @Param("year") Integer year);
    
    /**
     * 获取部门上一年度积分统计
     */
    DeptScoreAnalysisVO selectDeptLastYearStats(@Param("deptId") Long deptId, @Param("year") Integer year);
    
    /**
     * 获取部门月度积分趋势
     */
    List<ScoreTrendVO> selectDeptMonthlyTrend(@Param("deptId") Long deptId, @Param("year") Integer year);
    
    /**
     * 获取部门季度积分趋势
     */
    List<ScoreTrendVO> selectDeptQuarterlyTrend(@Param("deptId") Long deptId, @Param("year") Integer year);
    
    /**
     * 获取部门人均积分趋势
     */
    List<ScoreTrendVO> selectDeptAvgScoreTrend(@Param("deptId") Long deptId, @Param("year") Integer year);
    
    /**
     * 获取部门排名趋势
     */
    List<ScoreTrendVO> selectDeptRankTrend(@Param("deptId") Long deptId, @Param("year") Integer year);
    
    /**
     * 获取部门项目类型分布
     */
    List<ScoreTypeDistributionVO> selectDeptProjectTypeDistribution(@Param("deptId") Long deptId, @Param("year") Integer year);
} 