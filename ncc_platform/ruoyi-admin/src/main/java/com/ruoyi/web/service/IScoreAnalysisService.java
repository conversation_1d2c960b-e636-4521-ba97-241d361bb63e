package com.ruoyi.web.service;

import java.util.List;
import com.ruoyi.web.domain.vo.ScoreAnalysisVO;
import com.ruoyi.web.domain.vo.ScoreTrendVO;
import com.ruoyi.web.domain.vo.ScoreTypeDistributionVO;
import com.ruoyi.web.domain.vo.DeptScoreAnalysisVO;

public interface IScoreAnalysisService {
    /**
     * 获取用户年度积分分析数据
     */
    ScoreAnalysisVO getUserYearlyAnalysis(Integer year);
    
    /**
     * 获取用户积分趋势数据
     */
    List<ScoreTrendVO> getUserScoreTrend(Integer year, String timeUnit);
    
    /**
     * 获取用户项目类型分布
     */
    List<ScoreTypeDistributionVO> getUserProjectTypeDistribution(Integer year);

    /**
     * 获取部门年度积分分析数据
     */
    DeptScoreAnalysisVO getDeptYearlyAnalysis(Long deptId, Integer year);
    
    /**
     * 获取部门积分趋势数据
     */
    List<ScoreTrendVO> getDeptScoreTrend(Long deptId, Integer year, String timeUnit);
    
    /**
     * 获取部门人均积分趋势数据
     */
    List<ScoreTrendVO> getDeptAvgScoreTrend(Long deptId, Integer year);
    
    /**
     * 获取部门排名趋势数据
     */
    List<ScoreTrendVO> getDeptRankTrend(Long deptId, Integer year);
    
    /**
     * 获取部门项目类型分布
     */
    List<ScoreTypeDistributionVO> getDeptProjectTypeDistribution(Long deptId, Integer year);
} 