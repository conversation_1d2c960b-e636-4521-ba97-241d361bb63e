package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.web.domain.CompInstructorVO;
import com.ruoyi.web.domain.CompetitionBaseInfo;
import com.ruoyi.web.mapper.CompetitionBaseInfoMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.CompInstructor;
import com.ruoyi.web.service.ICompInstructorService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 竞赛指导教师Controller
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Api(value = "/web/instructor", tags = "竞赛指导教师")
@RestController
@RequestMapping("/web/instructor")
public class CompInstructorController extends BaseController
{
    @Autowired
    private ICompInstructorService compInstructorService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private CompetitionBaseInfoMapper competitionBaseInfoMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 查询竞赛指导教师列表
     */
    @ApiOperation("查询竞赛指导教师列表")
    @PreAuthorize("@ss.hasPermi('web:instructor:list')")
    @GetMapping("/list")
    public TableDataInfo list(CompInstructor compInstructor)
    {
        startPage();
        List<CompInstructor> list = compInstructorService.selectCompInstructorList(compInstructor);
        return getDataTable(list);
    }

    /**
     * 查询竞赛指导教师列表增强版
     */
    @PreAuthorize("@ss.hasPermi('web:instructor:list')")
    @ApiOperation("查询竞赛指导教师列表增强版")
    @GetMapping("/listPlus")
    public TableDataInfo listPlus(CompInstructor compInstructor)
    {
        startPage();
        List<CompInstructorVO> list = compInstructorService.selectCompInstructorListPlus(compInstructor);
        return getDataTable(list);
    }

    /**
     * 导出竞赛指导教师列表
     */
    @ApiOperation("导出竞赛指导教师列表")
    @PreAuthorize("@ss.hasPermi('web:instructor:export')")
    @Log(title = "竞赛指导教师", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CompInstructor compInstructor)
    {
        List<CompInstructor> list = compInstructorService.selectCompInstructorList(compInstructor);
        ExcelUtil<CompInstructor> util = new ExcelUtil<CompInstructor>(CompInstructor.class);
        util.exportExcel(response, list, "竞赛指导教师数据");
    }

    /**
     * 获取竞赛指导教师详细信息
     */
    @ApiOperation("获取竞赛指导教师详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(compInstructorService.selectCompInstructorById(id));
    }

    /**
     * 新增竞赛指导教师
     */
    @ApiOperation("新增竞赛指导教师")
    @Log(title = "竞赛指导教师", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CompInstructor compInstructor)
    {
        return toAjax(compInstructorService.insertCompInstructor(compInstructor));
    }

    /**
     * 修改竞赛指导教师
     */
    @ApiOperation("修改竞赛指导教师")
    @Log(title = "竞赛指导教师", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CompInstructor compInstructor)
    {
        return toAjax(compInstructorService.updateCompInstructor(compInstructor));
    }

    /**
     * 删除竞赛指导教师
     */
    @ApiOperation("删除竞赛指导教师")
    @Log(title = "竞赛指导教师", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(compInstructorService.deleteCompInstructorByIds(ids));
    }
}
