package com.ruoyi.web.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 积分规则详情对象 project2score_rule_detail
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class Project2scoreRuleDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private Long id;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String ruleName;

    /** 项目时间名称 */
    @Excel(name = "项目时间名称")
    private String projectTimeName;

    /** 申请名称标签 */
    @Excel(name = "申请名称标签")
    private String applyNameLabel;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Long ruleSort;

    /** 积分值 */
    @Excel(name = "积分值")
    private BigDecimal scoreValue;

    /** 是否需要填写金额（0否 1是） */
    @Excel(name = "是否需要填写金额", readConverterExp = "0=否,1=是")
    private String isAmount;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }
    public void setRuleName(String ruleName) 
    {
        this.ruleName = ruleName;
    }

    public String getRuleName() 
    {
        return ruleName;
    }
    public void setProjectTimeName(String projectTimeName) 
    {
        this.projectTimeName = projectTimeName;
    }

    public String getProjectTimeName() 
    {
        return projectTimeName;
    }
    public void setApplyNameLabel(String applyNameLabel) 
    {
        this.applyNameLabel = applyNameLabel;
    }

    public String getApplyNameLabel() 
    {
        return applyNameLabel;
    }
    public void setRuleSort(Long ruleSort) 
    {
        this.ruleSort = ruleSort;
    }

    public Long getRuleSort() 
    {
        return ruleSort;
    }
    public void setScoreValue(BigDecimal scoreValue) 
    {
        this.scoreValue = scoreValue;
    }

    public BigDecimal getScoreValue() 
    {
        return scoreValue;
    }
    public void setIsAmount(String isAmount) 
    {
        this.isAmount = isAmount;
    }

    public String getIsAmount() 
    {
        return isAmount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("categoryId", getCategoryId())
            .append("ruleName", getRuleName())
            .append("projectTimeName", getProjectTimeName())
            .append("applyNameLabel", getApplyNameLabel())
            .append("ruleSort", getRuleSort())
            .append("scoreValue", getScoreValue())
            .append("isAmount", getIsAmount())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
