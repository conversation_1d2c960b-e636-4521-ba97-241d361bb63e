package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 竞赛指导教师对象 comp_instructor
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@TableName(value = "comp_instructor")
@Data
@ApiModel(value = "CompInstructor", description = "竞赛指导教师对象")
public class CompInstructor
{
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /** 指导教师 */
    @Excel(name = "指导教师")
    @ApiModelProperty(value = "指导教师", example = "指导教师",required = true)
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 教师ID */
    @Excel(name = "教师ID")
    @TableField(exist = false)
    @ApiModelProperty(value = "教师ID", example = "教师ID",required = true)
    private String teacherId;

    @Excel(name = "指导老师姓名")
    @ApiModelProperty(value = "指导老师姓名", example = "指导老师姓名",required = true)
    @TableField(exist = false)
    private String teacherName;

    /** 教研室ID */
    @Excel(name = "教研室ID")
    @ApiModelProperty(value = "教研室ID", example = "教研室ID",required = true)
    @TableField(exist = false)
    private Long deptId;


    @Excel(name = "教研室名称")
    @TableField(exist = false)
    @ApiModelProperty(value = "教研室名称", example = "教研室名称",required = true)
    private String deptName;

    @Excel(name = "职务")
    @TableField(exist = false)
    @ApiModelProperty(value = "职务", example = "职务",required = true)
    private String job;

    @Excel(name = "职称")
    @TableField(exist = false)
    @ApiModelProperty(value = "职称", example = "职称",required = true)
    private String ranks;

    /** 比赛ID */
    @Excel(name = "比赛ID")
    @ApiModelProperty(value = "比赛ID", example = "比赛ID",required = true)
    private Long compId;

    @Excel(name = "比赛名称")
    @TableField(exist = false)
    @ApiModelProperty(value = "比赛名称", example = "比赛名称",required = true)
    private String compName;

    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "备注",required = true)
    private String remark;


}
