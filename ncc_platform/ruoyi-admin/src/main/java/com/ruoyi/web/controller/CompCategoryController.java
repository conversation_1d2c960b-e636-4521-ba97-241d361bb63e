package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.CompCategory;
import com.ruoyi.web.service.ICompCategoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 竞赛类别Controller
 * 
 * <AUTHOR>
 * @date 2024-04-22
 */
@Api(value = "/web/category", tags = "竞赛类别")
@RestController
@RequestMapping("/web/category")
public class CompCategoryController extends BaseController
{
    @Autowired
    private ICompCategoryService compCategoryService;

    /**
     * 查询竞赛类别列表
     */
    @ApiOperation("查询竞赛类别列表")
    @PreAuthorize("@ss.hasPermi('web:category:list')")
    @GetMapping("/list")
    public AjaxResult list(@ApiParam(value = "查询竞赛类别参数", required = true)CompCategory compCategory)
    {
        List<CompCategory> list = compCategoryService.selectCompCategoryList(compCategory);
        return success(list);
    }

    @ApiOperation("查询竞赛类别列表")
    @GetMapping("/list4Tree")
    public AjaxResult list4Tree(@ApiParam(value = "查询竞赛类别参数", required = true) CompCategory compCategory) {
        List<CompCategory> list = compCategoryService.selectCompCategoryList4Tree(compCategory);
        return success(list);
    }

    /**
     * 导出竞赛类别列表
     */
    @ApiOperation("导出竞赛类别列表")
    @PreAuthorize("@ss.hasPermi('web:category:export')")
    @Log(title = "竞赛类别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@ApiParam(value = "导出竞赛类别参数", required = true) HttpServletResponse response, CompCategory compCategory)
    {
        List<CompCategory> list = compCategoryService.selectCompCategoryList(compCategory);
        ExcelUtil<CompCategory> util = new ExcelUtil<CompCategory>(CompCategory.class);
        util.exportExcel(response, list, "竞赛类别数据");
    }

    /**
     * 获取竞赛类别详细信息
     */
    @ApiOperation("获取竞赛类别详细信息")
    @PreAuthorize("@ss.hasPermi('web:category:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") @ApiParam(value = "竞赛类别ID", required = true) Long id)
    {
        return success(compCategoryService.selectCompCategoryById(id));
    }

    /**
     * 新增竞赛类别
     */
    @ApiOperation("新增竞赛类别")
    @PreAuthorize("@ss.hasPermi('web:category:add')")
    @Log(title = "竞赛类别", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam(value = "新增竞赛类别参数", required = true)@RequestBody CompCategory compCategory)
    {
        return toAjax(compCategoryService.insertCompCategory(compCategory));
    }

    /**
     * 修改竞赛类别
     */
    @ApiOperation("修改竞赛类别")
    @PreAuthorize("@ss.hasPermi('web:category:edit')")
    @Log(title = "竞赛类别", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam(value = "修改竞赛类别参数", required = true)@RequestBody CompCategory compCategory)
    {
        return toAjax(compCategoryService.updateCompCategory(compCategory));
    }

    /**
     * 删除竞赛类别
     */
    @ApiOperation("删除竞赛类别")
    @PreAuthorize("@ss.hasPermi('web:category:remove')")
    @Log(title = "竞赛类别", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam(value = "竞赛类别ID数组", required = true)@PathVariable Long[] ids)
    {
        return toAjax(compCategoryService.deleteCompCategoryByIds(ids));
    }
}
