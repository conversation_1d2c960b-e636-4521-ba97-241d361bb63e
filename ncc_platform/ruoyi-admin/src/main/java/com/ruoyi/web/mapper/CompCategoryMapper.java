package com.ruoyi.web.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.domain.CompCategory;
import org.apache.ibatis.annotations.Mapper;

/**
 * 竞赛类别Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-22
 */
@Mapper
public interface CompCategoryMapper  extends BaseMapper<CompCategory>
{
    /**
     * 查询竞赛类别
     * 
     * @param id 竞赛类别主键
     * @return 竞赛类别
     */
    public CompCategory selectCompCategoryById(Long id);

    /**
     * 查询竞赛类别列表
     * 
     * @param compCategory 竞赛类别
     * @return 竞赛类别集合
     */
    public List<CompCategory> selectCompCategoryList(CompCategory compCategory);

    /**
     * 新增竞赛类别
     * 
     * @param compCategory 竞赛类别
     * @return 结果
     */
    public int insertCompCategory(CompCategory compCategory);

    /**
     * 修改竞赛类别
     * 
     * @param compCategory 竞赛类别
     * @return 结果
     */
    public int updateCompCategory(CompCategory compCategory);

    /**
     * 删除竞赛类别
     * 
     * @param id 竞赛类别主键
     * @return 结果
     */
    public int deleteCompCategoryById(Long id);

    /**
     * 批量删除竞赛类别
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompCategoryByIds(Long[] ids);
}
