package com.ruoyi.web.mapper;

import java.util.List;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.Project2scoreRecordVO;
import com.ruoyi.web.domain.vo.Project2scoreRecordExportVO;

/**
 * 积分记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface Project2scoreRecordMapper 
{
    /**
     * 查询积分记录
     * 
     * @param id 积分记录主键
     * @return 积分记录
     */
    public Project2scoreRecord selectProject2scoreRecordById(Long id);

    /**
     * 查询积分记录列表
     * 
     * @param project2scoreRecord 积分记录
     * @return 积分记录集合
     */
    public List<Project2scoreRecord> selectProject2scoreRecordList(Project2scoreRecord project2scoreRecord);

    /**
     * 新增积分记录
     * 
     * @param project2scoreRecord 积分记录
     * @return 结果
     */
    public int insertProject2scoreRecord(Project2scoreRecord project2scoreRecord);

    /**
     * 修改积分记录
     * 
     * @param project2scoreRecord 积分记录
     * @return 结果
     */
    public int updateProject2scoreRecord(Project2scoreRecord project2scoreRecord);

    /**
     * 删除积分记录
     * 
     * @param id 积分记录主键
     * @return 结果
     */
    public int deleteProject2scoreRecordById(Long id);

    /**
     * 批量删除积分记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProject2scoreRecordByIds(Long[] ids);

    /**
     * 根据项目ID删除积分记录
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    public int deleteProject2scoreRecordByProjectId(Long projectId);

    /**
     * 查询积分记录列表（关联用户、部门、项目、规则信息）
     *
     * @param record 积分记录
     * @return 积分记录集合
     */
    List<Project2scoreRecordVO> selectProject2scoreRecordVOList(Project2scoreRecord record);

    /**
     * 获取积分记录详细信息
     *
     * @param id 积分记录主键
     * @return 积分记录
     */
    Project2scoreRecordVO selectProject2scoreRecordVOById(Long id);

    /**
     * 查询积分记录导出列表
     * 
     * @param project2scoreRecord 查询条件
     * @return 积分记录导出数据集合
     */
    public List<Project2scoreRecordExportVO> selectExportList(Project2scoreRecord project2scoreRecord);
}
