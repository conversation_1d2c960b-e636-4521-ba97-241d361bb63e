package com.ruoyi.web.service;

import java.util.List;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.ScoreCompletionVO;

/**
 * 积分完成情况Service接口
 * 
 * <AUTHOR>
 */
public interface IScoreCompletionService
{
    /**
     * 查询积分完成情况列表
     * 
     * @param scoreCompletion 查询参数
     * @return 积分完成情况集合
     */
    public List<ScoreCompletionVO> selectScoreCompletionList(ScoreCompletionVO scoreCompletion);

    /**
     * 查询用户积分明细列表
     * 
     * @param userId 用户ID
     * @param params 查询参数
     * @return 积分记录集合
     */
    public List<Project2scoreRecord> selectScoreDetailsList(Long userId, Project2scoreRecord record);

    /**
     * 统计用户积分明细总数
     * 
     * @param userId 用户ID
     * @param params 查询参数
     * @return 总记录数
     */
    public int countScoreDetails(Long userId, Project2scoreRecord record);
} 