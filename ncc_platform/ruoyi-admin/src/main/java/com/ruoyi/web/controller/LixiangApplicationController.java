package com.ruoyi.web.controller;

import java.io.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.deepoove.poi.XWPFTemplate;

import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.ruoyi.common.config.RuoYiConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.ddr.poi.html.HtmlRenderPolicy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.LixiangApplication;
import com.ruoyi.web.service.ILixiangApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 立项信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Api(value = "/web/lixiang", description = "立项信息")
@RestController
@RequestMapping("/web/lixiang")
public class LixiangApplicationController extends BaseController
{
    @Autowired
    private ILixiangApplicationService lixiangApplicationService;

    /**
     * 查询立项信息列表
     */
    @ApiOperation("查询立项信息")
    @PreAuthorize("@ss.hasPermi('web:lixiang:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam(name = "lixiangApplication", value = "查询立项信息参数", required = true) LixiangApplication lixiangApplication)
    {
        startPage();
        List<LixiangApplication> list = lixiangApplicationService.selectLixiangApplicationList(lixiangApplication);
        return getDataTable(list);
    }

    /**
     * 导出立项文件
     */
    @ApiOperation("导出立项文件")
    @PreAuthorize("@ss.hasPermi('web:lixiang:export')")
    @Log(title = "立项信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@ApiParam(name = "lixiangApplication", value = "导出立项文件参数", required = true)HttpServletResponse response, LixiangApplication lixiangApplication) throws IOException {
        //列表行循环插件
        LoopRowTableRenderPolicy  policy = new LoopRowTableRenderPolicy();
        //HTML解析插件
        HtmlRenderPolicy htmlRenderPolicy = new HtmlRenderPolicy();
        Configure config = Configure.builder()
                .bind("指导老师", policy).bind("参赛选手", policy)
                .bind("竞赛信息", htmlRenderPolicy).bind("参赛情况", htmlRenderPolicy)
                .bind("竞赛内容", htmlRenderPolicy).bind("宣传方案", htmlRenderPolicy)
                .bind("组织方式", htmlRenderPolicy).bind("竞赛培训", htmlRenderPolicy)
                .build();

        Map<String, Object> dataList = lixiangApplicationService.getDataList(lixiangApplication);
        XWPFTemplate template = XWPFTemplate.compile(RuoYiConfig.getProfile() +"\\\\lixiang.docx", config).render(dataList);
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition","attachment;filename=\""+"out_template.docx"+"\"");

        // HttpServletResponse response
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        template.write(bos);
        template.close();
        bos.flush();
        bos.close();
        out.flush();
        out.close();
    }

    /**
     * 获取立项信息详细信息
     */
    @ApiOperation("获取立项信息详情")
    @PreAuthorize("@ss.hasPermi('web:lixiang:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(lixiangApplicationService.selectLixiangApplicationById(id));
    }

    /**
     * 新增立项信息
     */
    @ApiOperation("新增立项信息")
    @PreAuthorize("@ss.hasPermi('web:lixiang:add')")
    @Log(title = "立项信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam(name = "lixiangApplication", value = "新增立项信息参数", required = true)@RequestBody LixiangApplication lixiangApplication)
    {
        return toAjax(lixiangApplicationService.insertLixiangApplication(lixiangApplication));
    }

    /**
     * 修改立项信息
     */
    @ApiOperation("修改立项信息")
    @PreAuthorize("@ss.hasPermi('web:lixiang:edit')")
    @Log(title = "立项信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam(name = "lixiangApplication", value = "修改立项信息参数", required = true)@RequestBody LixiangApplication lixiangApplication)
    {
        return toAjax(lixiangApplicationService.updateLixiangApplication(lixiangApplication));
    }

    /**
     * 删除立项信息
     */
    @ApiOperation("删除立项信息")
    @PreAuthorize("@ss.hasPermi('web:lixiang:remove')")
    @Log(title = "立项信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lixiangApplicationService.deleteLixiangApplicationByIds(ids));
    }
}
