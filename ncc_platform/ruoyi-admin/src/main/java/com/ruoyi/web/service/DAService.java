package com.ruoyi.web.service;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.web.domain.CompCategory;
import com.ruoyi.web.domain.CompContestant;
import com.ruoyi.web.domain.CompetitionBaseInfo;
import com.ruoyi.web.mapper.*;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据分析Service
 */
@Service
public class DAService {

    @Resource
    private StudentInfoMapper studentInfoMapper;

    @Resource
    private CompContestantMapper compContestantMapper;

    @Resource
    private CompInstructorMapper compInstructorMapper;

    @Resource
    private CompetitionBaseInfoMapper competitionBaseInfoMapper;

    @Resource
    private LixiangApplicationMapper lixiangApplicationMapper;

    @Resource
    private JiexiangApplicationMapper jiexiangApplicationMapper;

    @Resource
    private CompCategoryMapper compCategoryMapper;


    public Map<String, Object> getCountNum() {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("studentNum", studentInfoMapper.selectCount(null));
        hashMap.put("competitionNum", competitionBaseInfoMapper.selectCount(null));
        hashMap.put("contestantNum", compContestantMapper.selectCount(null));
        hashMap.put("instructorNum", compInstructorMapper.selectCount(null));
        //查询非团体赛获奖数
        QueryWrapper<CompContestant> notHasGroupQueryWrapper = new QueryWrapper<>();
        notHasGroupQueryWrapper.ne("awards", "-1");
        notHasGroupQueryWrapper.isNotNull("awards");
        notHasGroupQueryWrapper.isNull("`group`");
        Long selectCount1 = compContestantMapper.selectCount(notHasGroupQueryWrapper);
        //查询团体赛获奖数
        QueryWrapper<CompContestant> hasGroupQueryWrapper = new QueryWrapper<>();
        hasGroupQueryWrapper.ne("awards", "-1");
        hasGroupQueryWrapper.isNotNull("awards");
        hasGroupQueryWrapper.isNotNull("`group`");
        //根据比赛id与队伍id 去重
        hasGroupQueryWrapper.select("DISTINCT `group`,competition_id");
        Long selectCount2 =compContestantMapper.selectCount(hasGroupQueryWrapper);
        hashMap.put("awardNum", selectCount1+selectCount2);
        return hashMap;
    }

    /***
     * 分析，主打的就是数据分析
     * 高质量考核 省教育厅A类赛 培育赛 行业赛 ,人社局,其他
     * @param major
     * @return
     */
    public Map<String, Object> getDataAnalysis(String major) {
        //查询高质量考核 省教育厅A类赛 培育赛 行业赛，并根据当前年份倒退五年去查询每个年份的id
        Map<String, Object> allCompetitionId = getAllCompetitionId();
        Map<String, Object> awardsNum = getAwardsNum(allCompetitionId, major);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("gaozhiliang",processingData((Map<String, Object>) awardsNum.get("高质量考核")));
        hashMap.put("aleisai",processingData((Map<String, Object>) awardsNum.get("省教育厅A类赛")) );
        hashMap.put("peiyusai",processingData((Map<String, Object>) awardsNum.get("培育赛")));
        hashMap.put("hangyesai", processingData((Map<String, Object>) awardsNum.get("行业赛")));
        hashMap.put("renshebu", processingData((Map<String, Object>) awardsNum.get("人社局")));
        hashMap.put("other", processingData((Map<String, Object>) awardsNum.get("其他")));
        hashMap.put("time",getTimeList());
        return hashMap;
    }

    /**
     * 处理所获取的数据转换为列表
     * @param data
     * @return
     */
    public List<Long> processingData(Map<String, Object> data){
        List<Long> list = new ArrayList<>();
        list.add(Long.valueOf(data.get("lastLastLastLastYear").toString()));
        list.add(Long.valueOf(data.get("lastLastLastYear").toString()));
        list.add(Long.valueOf(data.get("lastLastYear").toString()));
        list.add(Long.valueOf(data.get("lastYear").toString()));
        list.add(Long.valueOf(data.get("yearNow").toString()));
        return list;
    }

    /**
     * 获取四个类型的获奖情况
     *
     * @param major
     * @return
     */

    public Map<String, Object> getAwardsNum(Map<String, Object> competition, String major) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("高质量考核", getAwards((Map<String, Object>) competition.get("高质量考核"), major));
        hashMap.put("省教育厅A类赛", getAwards((Map<String, Object>) competition.get("省教育厅A类赛"), major));
        hashMap.put("培育赛", getAwards((Map<String, Object>) competition.get("培育赛"), major));
        hashMap.put("行业赛", getAwards((Map<String, Object>) competition.get("行业赛"), major));
        hashMap.put("人社局", getAwards((Map<String, Object>) competition.get("人社局"), major));
        hashMap.put("其他", getAwards((Map<String, Object>) competition.get("其他"), major));
        return hashMap;
    }

    /**
     * 查询获奖情况详情
     *
     * @return
     */
    public Map<String, Object> getAwards(Map<String, Object> competitionId, String major) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("yearNow", getAwardsData((List<Long>) competitionId.get("yearNow"), major));
        hashMap.put("lastYear", getAwardsData((List<Long>) competitionId.get("lastYear"), major));
        hashMap.put("lastLastYear", getAwardsData((List<Long>) competitionId.get("lastLastYear"), major));
        hashMap.put("lastLastLastYear", getAwardsData((List<Long>) competitionId.get("lastLastLastYear"), major));
        hashMap.put("lastLastLastLastYear", getAwardsData((List<Long>) competitionId.get("lastLastLastLastYear"), major));
        return hashMap;
    }

    public Long getAwardsData(List<Long> ids, String major) {
        //如果ids为空直接返回0
        if (ids == null || ids.size() == 0) {
            return 0L;
        }
        //查询非团体赛获奖数
        QueryWrapper<CompContestant> notHasGroupQueryWrapper = new QueryWrapper<>();
        notHasGroupQueryWrapper.ne("awards", "-1");
        notHasGroupQueryWrapper.isNotNull("awards");
        notHasGroupQueryWrapper.isNull("`group`");
        notHasGroupQueryWrapper.in("competition_id", ids);
        notHasGroupQueryWrapper.eq(StrUtil.isNotEmpty(major), "major", major);
        Long selectCount1 = compContestantMapper.selectCount(notHasGroupQueryWrapper);
        //查询团体赛获奖数
        QueryWrapper<CompContestant> hasGroupQueryWrapper = new QueryWrapper<>();
        hasGroupQueryWrapper.ne("awards", "-1");
        hasGroupQueryWrapper.isNotNull("awards");
        hasGroupQueryWrapper.isNotNull("`group`");
        hasGroupQueryWrapper.in("competition_id", ids);
        //根据比赛id与队伍id 去重
        hasGroupQueryWrapper.select("DISTINCT `group`,competition_id");
        hasGroupQueryWrapper.eq(StrUtil.isNotEmpty(major), "major", major);
        Long selectCount2 =compContestantMapper.selectCount(hasGroupQueryWrapper);
        return selectCount1+selectCount2;

    }

    /**
     * 按照年份获取所有比赛id
     *
     * @return
     */
    public Map<String, Object> getAllCompetitionId() {
        HashMap<String, Object> hashMap = new HashMap<>();
        Map<String, Object> time = getTime();
        hashMap.put("高质量考核", getCompetitionData(time, 1L));
        hashMap.put("省教育厅A类赛", getCompetitionData(time, 2L));
        hashMap.put("培育赛", getCompetitionData(time, 3L));
        hashMap.put("行业赛", getCompetitionData(time, 4L));
        hashMap.put("人社局", getCompetitionData(time, 9L));
        hashMap.put("其他", getCompetitionData(time, 10L));
        return hashMap;
    }

    /**
     * 获取对应分类的比赛id数据
     * TODO 缓存优化
     *
     * @param time
     * @param type
     * @return
     */
    @Cacheable(value = "competitionDataCache", key = "{#type, T(java.time.LocalDate).now().toString()}")
    public Map<String, Object> getCompetitionData(Map<String, Object> time, Long type) {
        HashMap<String, Object> hashMap = new HashMap<>();
        //创建搜索条件对象
        QueryWrapper<CompetitionBaseInfo> yearNow = new QueryWrapper<>();
        QueryWrapper<CompetitionBaseInfo> lastYear = new QueryWrapper<>();
        QueryWrapper<CompetitionBaseInfo> lastLastYear = new QueryWrapper<>();
        QueryWrapper<CompetitionBaseInfo> lastLastLastYear = new QueryWrapper<>();
        QueryWrapper<CompetitionBaseInfo> lastLastLastLastYear = new QueryWrapper<>();
        //设置年份
        yearNow.between("match_time", time.get("beginOfYearNow"), time.get("endOfYearNow"));
        lastYear.between("match_time", time.get("beginOfLastYear"), time.get("endOfLastYear"));
        lastLastYear.between("match_time", time.get("beginOfLastLastYear"), time.get("endOfLastLastYear"));
        lastLastLastYear.between("match_time", time.get("beginOfLastLastLastYear"), time.get("endOfLastLastLastYear"));
        lastLastLastLastYear.between("match_time", time.get("beginOfLastLastLastLastYear"), time.get("endOfLastLastLastLastYear"));
        //限制只查询id字段
        yearNow.select("id");
        lastYear.select("id");
        lastLastYear.select("id");
        lastLastLastYear.select("id");
        lastLastLastLastYear.select("id");
        // 限制查询分类
        yearNow.in("category", getCompetitionCategoryIdsDeep(type));
        lastYear.in("category", getCompetitionCategoryIdsDeep(type));
        lastLastYear.in("category", getCompetitionCategoryIdsDeep(type));
        lastLastLastYear.in("category", getCompetitionCategoryIdsDeep(type));
        lastLastLastLastYear.in("category", getCompetitionCategoryIdsDeep(type));
        // 限制查询状态
        yearNow.eq("status", 4);
        lastYear.eq("status", 4);
        lastLastYear.eq("status", 4);
        lastLastLastYear.eq("status", 4);
        lastLastLastLastYear.eq("status", 4);
        // 查询并存入数据
        hashMap.put("yearNow", getCompetitionIdList(competitionBaseInfoMapper.selectList(yearNow)));
        hashMap.put("lastYear", getCompetitionIdList(competitionBaseInfoMapper.selectList(lastYear)));
        hashMap.put("lastLastYear", getCompetitionIdList(competitionBaseInfoMapper.selectList(lastLastYear)));
        hashMap.put("lastLastLastYear", getCompetitionIdList(competitionBaseInfoMapper.selectList(lastLastLastYear)));
        hashMap.put("lastLastLastLastYear", getCompetitionIdList(competitionBaseInfoMapper.selectList(lastLastLastLastYear)));
        return hashMap;
    }

    /**
     * 获取所有的ID转换为列表
     *
     * @param competitionBaseInfoList
     * @return
     */
    public List<Long> getCompetitionIdList(List<CompetitionBaseInfo> competitionBaseInfoList) {
        // 检查输入列表是否为null，避免NullPointerException
        if (competitionBaseInfoList == null) {
            return Collections.emptyList();
        }

        // 使用stream处理数据，映射每个元素的ID，并确保结果非null
        return competitionBaseInfoList.stream()
                .map(CompetitionBaseInfo::getId)
                .filter(id -> id != null) // 过滤掉null ID，增加数据安全性
                .collect(Collectors.toList());
    }

    /**
     * 获取某分类下所有的竞赛类别的id
     *
     * @param id
     * @return
     */
    public List<Long> getCompetitionCategoryIdsDeep(Long id) {
        ArrayList<Long> longs = new ArrayList<Long>();
        if (id != null) {
            // 获取当前分类下的子分类
            List<CompCategory> children = compCategoryMapper.selectList(new QueryWrapper<CompCategory>().eq("father_id", id));
            // 把每个分类的id传给longs
            children.forEach(compCategory -> {
                longs.add(compCategory.getId());
            });
        }
        longs.add(id);
        return longs;
    }

    /**
     * 获取年份
     *
     * @return
     */
    @CachePut(cacheNames = {"getTime"})
    public Map<String, Object> getTime() {
        //获取 今年开始时间和结束时间 去年开始时间与结束时间 前年开始时间与结束时间 具体看代码，存入到一个HashMap中
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("beginOfYearNow", DateUtil.beginOfYear(DateUtil.date()));
        hashMap.put("endOfYearNow", DateUtil.endOfYear(DateUtil.date()));
        hashMap.put("yearNow", DateUtil.year(DateUtil.date()));
        hashMap.put("beginOfLastYear", DateUtil.beginOfYear(DateUtil.offsetMonth(DateUtil.date(), -12)));
        hashMap.put("endOfLastYear", DateUtil.endOfYear(DateUtil.offsetMonth(DateUtil.date(), -12)));
        hashMap.put("lastYear", DateUtil.year(DateUtil.offsetMonth(DateUtil.date(), -12)));
        hashMap.put("beginOfLastLastYear", DateUtil.beginOfYear(DateUtil.offsetMonth(DateUtil.date(), -12 * 2)));
        hashMap.put("endOfLastLastYear", DateUtil.endOfYear(DateUtil.offsetMonth(DateUtil.date(), -12 * 2)));
        hashMap.put("lastLastYear", DateUtil.year(DateUtil.offsetMonth(DateUtil.date(), -12 * 2)));
        hashMap.put("beginOfLastLastLastYear", DateUtil.beginOfYear(DateUtil.offsetMonth(DateUtil.date(), -12 * 3)));
        hashMap.put("endOfLastLastLastYear", DateUtil.endOfYear(DateUtil.offsetMonth(DateUtil.date(), -12 * 3)));
        hashMap.put("lastLastLastYear", DateUtil.year(DateUtil.offsetMonth(DateUtil.date(), -12 * 3)));
        hashMap.put("beginOfLastLastLastLastYear", DateUtil.beginOfYear(DateUtil.offsetMonth(DateUtil.date(), -12 * 4)));
        hashMap.put("endOfLastLastLastLastYear", DateUtil.endOfYear(DateUtil.offsetMonth(DateUtil.date(), -12 * 4)));
        hashMap.put("lastLastLastLastYear", DateUtil.year(DateUtil.offsetMonth(DateUtil.date(), -12 * 4)));
        return hashMap;
    }

    public List<Integer> getTimeList() {
        ArrayList<Integer> longs = new ArrayList<>();
        for (int i = 4; i >= 0; i--) {
            longs.add(DateUtil.year(DateUtil.offsetMonth(DateUtil.date(), -12 * i)));
        }
        return longs;
    }
}
