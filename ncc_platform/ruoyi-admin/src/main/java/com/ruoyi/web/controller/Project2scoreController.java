package com.ruoyi.web.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.web.domain.vo.Project2scoreExportVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.Project2score;
import com.ruoyi.web.domain.Project2scoreDistribution;
import com.ruoyi.web.service.IProject2scoreService;
import com.ruoyi.web.service.IProject2scoreDistributionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.alibaba.fastjson.JSON;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Arrays;

/**
 * 项目积分Controller
 * TODO 回显的内容应该是老师姓名，就不要直接显示ID了
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/web/project2score")
public class Project2scoreController extends BaseController
{
    @Autowired
    private IProject2scoreService project2scoreService;

    @Autowired
    private IProject2scoreDistributionService distributionService;

    /**
     * 查询项目积分列表
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:list')")
    @GetMapping("/list")
    public TableDataInfo list(Project2score project2score)
    {
        startPage();
        List<Project2score> list = project2scoreService.selectProject2scoreList(project2score);
        return getDataTable(list);
    }

    /**
     * 查询当前用户的项目积分列表（按状态排序）
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:list')")
    @GetMapping("/myList")
    public TableDataInfo myList(Project2score project2score)
    {
        startPage();
        // 设置当前用户ID
        project2score.setUserId(getUserId());
        List<Project2score> list = project2scoreService.selectMyProject2scoreList(project2score);
        return getDataTable(list);
    }

    /**
     * 查询项目积分列表（按状态排序：待审核->已驳回->已通过->草稿）
     */
    @GetMapping("/auditList")
    public TableDataInfo auditList(Project2score project2score)
    {
        startPage();
        List<Project2score> list = project2scoreService.selectProject2scoreAuditList(project2score);
        return getDataTable(list);
    }

    /**
     * 导出项目积分列表
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:export')")
    @Log(title = "项目积分", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Project2score project2score)
    {
        List<Project2scoreExportVO> list = project2scoreService.selectProject2scoreExportList(project2score);
        ExcelUtil<Project2scoreExportVO> util = new ExcelUtil<Project2scoreExportVO>(Project2scoreExportVO.class);
        util.exportExcel(response, list, "项目积分数据");
    }

    /**
     * 导出当前用户的项目积分列表
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:export')")
    @Log(title = "项目积分", businessType = BusinessType.EXPORT)
    @PostMapping("/myExport")
    public void myExport(HttpServletResponse response, Project2score project2score)
    {
        // 设置当前用户ID
        project2score.setUserId(getUserId());
        List<Project2scoreExportVO> list = project2scoreService.selectProject2scoreExportList(project2score);
        ExcelUtil<Project2scoreExportVO> util = new ExcelUtil<Project2scoreExportVO>(Project2scoreExportVO.class);
        util.exportExcel(response, list, "我的项目积分数据");
    }

    /**
     * 获取项目积分详细信息
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(project2scoreService.selectProject2scoreById(id));
    }

    /**
     * 新增项目积分
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:add')")
    @Log(title = "项目积分", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Map<String, Object> params)
    {
        try {
            // 1. 解析项目基本信息
            Map<String, Object> projectMap = (Map<String, Object>) params.get("project");
            if (projectMap == null) {
                return error("项目信息不能为空");
            }

            Project2score project = new Project2score();
            project.setApplyName(projectMap.get("applyName").toString());
            project.setProjectTime(DateUtils.parseDate(projectMap.get("projectTime").toString()));
            project.setScore(new BigDecimal(projectMap.get("score").toString()));
            project.setRuleId(Long.valueOf(projectMap.get("ruleId").toString()));
            if (projectMap.get("categoryId") != null) {
                project.setCategoryId(Long.valueOf(projectMap.get("categoryId").toString()));
            }
            project.setFilePath(projectMap.get("filePath") != null ? projectMap.get("filePath").toString() : null);
            project.setRemark(projectMap.get("remark") != null ? projectMap.get("remark").toString() : null);
            project.setStatus(projectMap.get("status") != null ? projectMap.get("status").toString() : "1");
            project.setIsTeam(projectMap.get("isTeam").toString());
            
            // 设置当前用户信息
            project.setUserId(getUserId());
            project.setDeptId(getDeptId());
            project.setCreateBy(getUsername());
            
            List<Project2scoreDistribution> distributions = null;
            // 判断是否为团队项目
            if ("1".equals(project.getIsTeam())) {
                // 2. 验证团队成员信息
                if (!params.containsKey("teamMembers")) {
                    return error("团队项目必须包含团队成员信息");
                }
                List<Map<String, Object>> teamMembers = (List<Map<String, Object>>) params.get("teamMembers");
                if (teamMembers == null || teamMembers.isEmpty()) {
                    return error("团队成员不能为空");
                }

                // 3. 初始化团队成员列表
                distributions = new ArrayList<>();
                BigDecimal totalRatio = BigDecimal.ZERO;
                BigDecimal totalScore = BigDecimal.ZERO;
                boolean hasLeader = false;

                // 4. 处理每个团队成员
                for (Map<String, Object> member : teamMembers) {
                    try {
                        Project2scoreDistribution distribution = new Project2scoreDistribution();
                        
                        // 4.1 验证成员类型
                        if (!member.containsKey("memberType")) {
                            return error("成员类型不能为空");
                        }
                        String memberType = member.get("memberType").toString();
                        distribution.setMemberType(memberType);

                        // 4.2 根据成员类型设置不同的字段
                        if ("0".equals(memberType)) {
                            // 院内用户
                            if (!member.containsKey("userId") || member.get("userId") == null) {
                                return error("院内用户的ID不能为空");
                            }
                            distribution.setUserId(Long.valueOf(member.get("userId").toString()));
                            distribution.setMemberName(member.get("memberName").toString());
                            // 设置部门ID
                            if (member.containsKey("deptId") && member.get("deptId") != null) {
                                distribution.setDeptId(Long.valueOf(member.get("deptId").toString()));
                            }
                        } else if ("1".equals(memberType)) {
                            // 外部人员
                            if (!member.containsKey("memberName") || member.get("memberName") == null) {
                                return error("外部人员的姓名不能为空");
                            }
                            distribution.setMemberName(member.get("memberName").toString());
                            distribution.setUserId(0L);
                            distribution.setDeptId(null); // 外部人员没有部门ID
                            if (member.containsKey("memberIdentity") && member.get("memberIdentity") != null) {
                                distribution.setMemberIdentity(member.get("memberIdentity").toString());
                            }
                        } else {
                            return error("无效的成员类型: " + memberType);
                        }

                        // 4.3 验证并设置分配比例
                        if (!member.containsKey("ratio") || member.get("ratio") == null) {
                            return error("分配比例不能为空");
                        }
                        BigDecimal ratio = new BigDecimal(member.get("ratio").toString());
                        // 分配比例只需要不超过100即可
                        if (ratio.compareTo(BigDecimal.ZERO) < 0) {
                            return error("分配比例不能为负数");
                        }
                        if (ratio.compareTo(new BigDecimal("100")) > 0) {
                            return error("分配比例不能超过100");
                        }
                        distribution.setRatio(ratio);
                        totalRatio = totalRatio.add(ratio);

                        // 4.4 验证并设置分配积分
                        if (!member.containsKey("score") || member.get("score") == null) {
                            return error("分配积分不能为空");
                        }
                        BigDecimal score = new BigDecimal(member.get("score").toString());
                        // 分配积分只需要不超过项目总积分即可
                        if (score.compareTo(BigDecimal.ZERO) < 0) {
                            return error("分配积分不能为负数");
                        }
                        if (score.compareTo(project.getScore()) > 0) {
                            return error("分配积分不能超过项目总积分");
                        }
                        distribution.setScore(score);
                        totalScore = totalScore.add(score);

                        // 4.5 验证并设置是否为负责人
                        if (!member.containsKey("isLeader") || member.get("isLeader") == null) {
                            return error("请指定是否为负责人");
                        }
                        String isLeader = member.get("isLeader").toString();
                        if (!"0".equals(isLeader) && !"1".equals(isLeader)) {
                            return error("无效的负责人标识");
                        }
                        distribution.setIsLeader(isLeader);
                        if ("1".equals(isLeader)) {
                            if (hasLeader) {
                                return error("只能指定一个团队负责人");
                            }
                            hasLeader = true;
                        }

                        // 4.6 设置创建人信息
                        distribution.setCreateBy(getUsername());
                        distributions.add(distribution);
                        
                    } catch (NumberFormatException e) {
                        return error("数据格式错误: " + e.getMessage());
                    } catch (Exception e) {
                        return error("处理团队成员数据时出错: " + e.getMessage());
                    }
                }

                // 5. 验证团队整体数据
                if (!hasLeader) {
                    return error("必须指定一个团队负责人");
                }
                if (totalRatio.compareTo(new BigDecimal("100")) != 0) {
                    return error("团队成员分配比例之和必须等于100%");
                }
                if (totalScore.compareTo(project.getScore()) != 0) {
                    return error("团队成员分配积分之和必须等于项目总积分");
                }

                // 6. 保存项目和团队成员数据
                return success(project2scoreService.insertProject2scoreWithTeam(project, distributions));
            } else {
                return success(project2scoreService.insertProject2score(project));
            }
        } catch (Exception e) {
            return error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 修改项目积分
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:edit')")
    @Log(title = "项目积分", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Map<String, Object> params) {
        try {
            // 1. 获取并验证原有项目信息
            Map<String, Object> projectMap = (Map<String, Object>) params.get("project");
            Long projectId = Long.valueOf(projectMap.get("id").toString());
            Project2score originalProject = project2scoreService.selectProject2scoreById(projectId);
            if (originalProject == null) {
                return error("项目不存在");
            }

            // 2. 验证是否是项目创建人
            if (!originalProject.getUserId().equals(getUserId())) {
                return error("只能修改自己创建的项目");
            }

            // 3. 更新项目信息，保留原有的一些字段
            Project2score project = new Project2score();
            project.setId(projectId);
            project.setUserId(originalProject.getUserId());
            project.setDeptId(originalProject.getDeptId());
            project.setUpdateBy(getUsername());
            project.setUpdateTime(DateUtils.getNowDate());
            
            // 设置新的项目信息
            project.setRuleId(Long.valueOf(projectMap.get("ruleId").toString()));
            if (projectMap.get("categoryId") != null) {
                project.setCategoryId(Long.valueOf(projectMap.get("categoryId").toString()));
            }
            project.setProjectTime(DateUtils.parseDate(projectMap.get("projectTime").toString()));
            if (projectMap.get("amount") != null) {
                project.setAmount(new BigDecimal(projectMap.get("amount").toString()));
            }
            project.setScore(new BigDecimal(projectMap.get("score").toString()));
            project.setApplyName(projectMap.get("applyName") != null ? projectMap.get("applyName").toString() : null);
            project.setFileName(projectMap.get("fileName") != null ? projectMap.get("fileName").toString() : null);
            project.setFilePath(projectMap.get("filePath") != null ? projectMap.get("filePath").toString() : null);
            project.setRemark(projectMap.get("remark") != null ? projectMap.get("remark").toString() : null);
            project.setStatus(projectMap.get("status") != null ? projectMap.get("status").toString() : originalProject.getStatus());
            project.setIsTeam(projectMap.get("isTeam").toString());
            
            List<Project2scoreDistribution> distributions = null;
            // 判断是否为团队项目
            if ("1".equals(project.getIsTeam())) {
                // 团队项目必须包含团队成员
                if (!params.containsKey("teamMembers") || ((List<Map<String, Object>>) params.get("teamMembers")).isEmpty()) {
                    return error("团队项目必须包含团队成员信息");
                }
                distributions = new ArrayList<>();
                List<Map<String, Object>> teamMembers = (List<Map<String, Object>>) params.get("teamMembers");
                
                // 验证团队成员数据
                BigDecimal totalRatio = BigDecimal.ZERO;
                BigDecimal totalScore = BigDecimal.ZERO;
                boolean hasLeader = false;
                
                for (Map<String, Object> member : teamMembers) {
                    Project2scoreDistribution distribution = new Project2scoreDistribution();
                    distribution.setProjectId(projectId);
                    
                    // 设置成员类型
                    if (!member.containsKey("memberType")) {
                        return error("成员类型不能为空");
                    }
                    String memberType = member.get("memberType").toString();
                    distribution.setMemberType(memberType);
                    
                    // 根据成员类型设置不同的字段
                    if ("0".equals(memberType)) {
                        // 院内用户
                        if (!member.containsKey("userId") || member.get("userId") == null) {
                            return error("院内用户的ID不能为空");
                        }
                        distribution.setUserId(Long.valueOf(member.get("userId").toString()));
                        distribution.setMemberName(member.get("memberName").toString());
                        // 设置部门ID
                        if (member.containsKey("deptId") && member.get("deptId") != null) {
                            distribution.setDeptId(Long.valueOf(member.get("deptId").toString()));
                        }
                    } else if ("1".equals(memberType)) {
                        // 外部人员
                        if (!member.containsKey("memberName") || member.get("memberName") == null) {
                            return error("外部人员的姓名不能为空");
                        }
                        distribution.setMemberName(member.get("memberName").toString());
                        distribution.setUserId(0L);
                        distribution.setDeptId(null); // 外部人员没有部门ID
                        if (member.containsKey("memberIdentity") && member.get("memberIdentity") != null) {
                            distribution.setMemberIdentity(member.get("memberIdentity").toString());
                        }
                    } else {
                        return error("无效的成员类型: " + memberType);
                    }

                    // 验证并设置分配比例
                    if (!member.containsKey("ratio") || member.get("ratio") == null) {
                        return error("分配比例不能为空");
                    }
                    BigDecimal ratio = new BigDecimal(member.get("ratio").toString());
                    if (ratio.compareTo(BigDecimal.ZERO) < 0) {
                        return error("分配比例不能为负数");
                    }
                    if (ratio.compareTo(new BigDecimal("100")) > 0) {
                        return error("分配比例不能超过100");
                    }
                    distribution.setRatio(ratio);
                    totalRatio = totalRatio.add(ratio);

                    // 验证并设置分配积分
                    if (!member.containsKey("score") || member.get("score") == null) {
                        return error("分配积分不能为空");
                    }
                    BigDecimal score = new BigDecimal(member.get("score").toString());
                    if (score.compareTo(BigDecimal.ZERO) < 0) {
                        return error("分配积分不能为负数");
                    }
                    if (score.compareTo(project.getScore()) > 0) {
                        return error("分配积分不能超过项目总积分");
                    }
                    distribution.setScore(score);
                    totalScore = totalScore.add(score);

                    // 验证并设置是否为负责人
                    if (!member.containsKey("isLeader") || member.get("isLeader") == null) {
                        return error("请指定是否为负责人");
                    }
                    String isLeader = member.get("isLeader").toString();
                    if (!"0".equals(isLeader) && !"1".equals(isLeader)) {
                        return error("无效的负责人标识");
                    }
                    distribution.setIsLeader(isLeader);
                    if ("1".equals(isLeader)) {
                        if (hasLeader) {
                            return error("只能指定一个团队负责人");
                        }
                        hasLeader = true;
                    }

                    distribution.setUpdateBy(getUsername());
                    distribution.setUpdateTime(DateUtils.getNowDate());
                    distributions.add(distribution);
                }

                // 验证团队整体数据
                if (!hasLeader) {
                    return error("必须指定一个团队负责人");
                }
                if (totalRatio.compareTo(new BigDecimal("100")) != 0) {
                    return error("团队成员分配比例之和必须等于100%");
                }
                if (totalScore.compareTo(project.getScore()) != 0) {
                    return error("团队成员分配积分之和必须等于项目总积分");
                }

                return toAjax(project2scoreService.updateProject2scoreWithTeam(project, distributions));
            } else {
                return toAjax(project2scoreService.updateProject2score(project));
            }
        } catch (Exception e) {
            return error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 提交项目积分审核
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:edit')")
    @Log(title = "项目积分", businessType = BusinessType.UPDATE)
    @PutMapping("/submit/{id}")
    public AjaxResult submit(@PathVariable("id") Long id)
    {
        // 1. 验证项目是否存在
        Project2score project = project2scoreService.selectProject2scoreById(id);
        if (project == null) {
            return AjaxResult.error("项目不存在");
        }

        // 2. 验证项目状态
        if (!Arrays.asList("0", "3").contains(project.getStatus())) {
            return AjaxResult.error("只有草稿和驳回状态的项目可以提交审核");
        }

        // 3. 验证是否是项目创建人
        if (!project.getUserId().equals(getUserId())) {
            return AjaxResult.error("只能提交自己创建的项目");
        }

        // 4. 更新状态为待审核
        project.setStatus("1");
        project.setUpdateTime(DateUtils.getNowDate());
        project.setUpdateBy(getUsername());
        
        return toAjax(project2scoreService.updateProject2score(project));
    }

    /**
     * 撤回项目积分审核
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:edit')")
    @Log(title = "项目积分", businessType = BusinessType.UPDATE)
    @PutMapping("/revoke/{id}")
    public AjaxResult revoke(@PathVariable("id") Long id)
    {
        // 1. 验证项目是否存在
        Project2score project = project2scoreService.selectProject2scoreById(id);
        if (project == null) {
            return AjaxResult.error("项目不存在");
        }

        // 2. 验证项目状态
        if ("0".equals(project.getStatus()) || "1".equals(project.getStatus())) {
            return AjaxResult.error("只能撤回已审核的项目");
        }

        try {
            return toAjax(project2scoreService.revokeProject2score(id, getUserId()));
        } catch (Exception e) {
            return AjaxResult.error("撤回失败：" + e.getMessage());
        }
    }

    /**
     * 删除项目积分
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:remove')")
    @Log(title = "项目积分", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        try {
            // 删除项目积分及相关数据
            return toAjax(project2scoreService.deleteProject2scoreByIds(ids));
        } catch (Exception e) {
            return error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取项目团队成员
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:query')")
    @GetMapping("/teamMembers/{projectId}")
    public AjaxResult getTeamMembers(@PathVariable("projectId") Long projectId)
    {
        return success(distributionService.selectProject2scoreDistributionByProjectId(projectId));
    }

    /**
     * 更新项目团队成员
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:edit')")
    @Log(title = "项目积分", businessType = BusinessType.UPDATE)
    @PutMapping("/teamMembers/{projectId}")
    public AjaxResult updateTeamMembers(@PathVariable("projectId") Long projectId, @RequestBody List<Project2scoreDistribution> members)
    {
        try {
            // 1. 验证项目是否存在
            Project2score project = project2scoreService.selectProject2scoreById(projectId);
            if (project == null) {
                return error("项目不存在");
            }

            // 2. 验证是否是项目创建人
            if (!project.getUserId().equals(getUserId())) {
                return error("只能修改自己创建的项目");
            }

            // 3. 验证项目状态
            if (!Arrays.asList("0", "2", "3").contains(project.getStatus())) {
                return error("只能在草稿、驳回或撤回状态下修改团队成员");
            }

            // 4. 验证是否为团队项目
            if (!"1".equals(project.getIsTeam())) {
                return error("非团队项目不能设置团队成员");
            }

            // 5. 验证团队成员数据
            if (members == null || members.isEmpty()) {
                return error("团队成员不能为空");
            }

            // 6. 验证是否有负责人
            boolean hasLeader = false;
            BigDecimal totalRatio = BigDecimal.ZERO;
            BigDecimal totalScore = BigDecimal.ZERO;

            for (Project2scoreDistribution member : members) {
                if ("1".equals(member.getIsLeader())) {
                    hasLeader = true;
                }
                totalRatio = totalRatio.add(member.getRatio());
                totalScore = totalScore.add(member.getScore());
            }

            if (!hasLeader) {
                return error("必须指定一个团队负责人");
            }

            // 7. 验证分配比例
            if (totalRatio.compareTo(new BigDecimal("100")) != 0) {
                return error("团队成员分配比例之和必须等于100%");
            }

            // 8. 验证分配积分
            if (totalScore.compareTo(project.getScore()) != 0) {
                return error("团队成员分配积分之和必须等于项目总积分");
            }

            // 9. 设置项目ID和创建人信息
            for (Project2scoreDistribution member : members) {
                member.setProjectId(projectId);
                member.setCreateBy(getUsername());
            }

            // 10. 更新团队成员
            return toAjax(distributionService.updateProject2scoreDistributionBatch(projectId, members));
        } catch (Exception e) {
            return error("更新团队成员失败：" + e.getMessage());
        }
    }
}
