package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 竞赛选手对象 comp_contestant
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */

@Data
@TableName(value = "comp_contestant")
@ApiModel(value = "竞赛选手对象", description = "竞赛选手对象")
public class CompContestantDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Excel(name = "主键")
    @ApiModelProperty(value = "主键", required = true)
    private Long id;

    /** 比赛ID */
    @Excel(name = "比赛ID")
    @ApiModelProperty(value = "比赛ID", required = true)
    private Long competitionId;

    /** 选手ID */
    @Excel(name = "选手ID")
    @ApiModelProperty(value = "选手ID", required = true)
    private String contestantId;
    @Excel(name = "姓名")
    @ApiModelProperty(value = "学生姓名", required = true)
    private String name;


    private String awards;

    /** 选手类型*/
    @Excel(name = "选手类型",dictType= "contestant_type")
    @ApiModelProperty(value = "选手类型(0:学生,1:老师)", example = "选手类型(0:学生,1:老师)",required = true)
    private Integer type;

    /** 学生专业 */
    @Excel(name = "专业",dictType = "major",prompt = "目前仅支持智能工程学院的专业批量导入，其他学院请通过单个选手添加写入数据",
    combo = {"软件技术","计算机应用技术","计算机网络技术","云计算应用技术","智能控制技术","电子工程技术","新能源汽车技术"})
    @ApiModelProperty(value = "学生专业", required = true)
    private String major;

    /** 学生班级 */
    @Excel(name = "班级",combo={"1班","2班","3班","4班","5班"},prompt = "班级格式应为阿拉伯数字+班，例如（1班）")
    @ApiModelProperty(value = "学生班级", required = true)
    private String className;

    /** 学生学号 */
    @Excel(name = "学号")
    @ApiModelProperty(value = "学号/工号", example = "学号/工号",required = true)
    private String no;
    /** 选手角色 */
    @Excel(name = "职责及分工")
    @ApiModelProperty(value = "职责及分工", required = true)
    private String contestantRole;
    /** 队伍 */
    @Excel(name = "队伍",prompt = "班级格式应为阿拉伯数字+队，例如（1队），非团体赛请勿填写",dictType = "group",
            combo = {"1队", "2队", "3队", "4队", "5队", "6队", "7队", "8队", "9队", "10队",
                    "11队", "12队", "13队", "14队", "15队", "16队", "17队", "18队", "19队",
                    "20队",})
    @ApiModelProperty(value = "队伍", example = "队伍",required = true)
    private String group;
    /** 性别 */
    @Excel(name = "性别", dictType = "sys_user_sex" ,combo={"男","女"})
    @ApiModelProperty(value = "性别", required = true)
    private Integer gender;


}
