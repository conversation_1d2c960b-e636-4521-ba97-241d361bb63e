package com.ruoyi.web.service;

import java.util.List;

import com.ruoyi.web.domain.CompContestant;
import com.ruoyi.web.domain.CompContestantDTO;
import com.ruoyi.web.domain.CompContestantVO;
import org.springframework.stereotype.Service;

/**
 * 竞赛选手Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public interface ICompContestantService 
{
    /**
     * 查询竞赛选手
     * 
     * @param id 竞赛选手主键
     * @return 竞赛选手
     */
    public CompContestant selectCompContestantById(Long id);

    /**
     * 查询竞赛选手列表
     * 
     * @param compContestant 竞赛选手
     * @return 竞赛选手集合
     */
    public List<CompContestant> selectCompContestantList(CompContestant compContestant);

    List<CompContestantVO> selectCompContestantListPro(CompContestantVO compContestant);

    /**
     * 新增竞赛选手
     * 
     * @param compContestant 竞赛选手
     * @return 结果
     */
    public int insertCompContestant(CompContestantDTO compContestant);

    /**
     * 修改竞赛选手
     * 
     * @param compContestant 竞赛选手
     * @return 结果
     */
    public int updateCompContestant(CompContestant compContestant);

    /**
     * 批量删除竞赛选手
     * 
     * @param ids 需要删除的竞赛选手主键集合
     * @return 结果
     */
    public int deleteCompContestantByIds(Long[] ids);

    /**
     * 删除竞赛选手信息
     * 
     * @param id 竞赛选手主键
     * @return 结果
     */
    public int deleteCompContestantById(Long id);

    /**
     * 查询竞赛选手列表增强版本
     * @param compContestant
     * @return
     */
    List<CompContestantVO> selectCompContestantListPlus(CompContestantVO compContestant);

    String importCompContestant(List<CompContestantDTO> studentList, String operName, Long competitionId,Long competitionType);

    int updateCompContestants(List<CompContestant> compContestants);
}
