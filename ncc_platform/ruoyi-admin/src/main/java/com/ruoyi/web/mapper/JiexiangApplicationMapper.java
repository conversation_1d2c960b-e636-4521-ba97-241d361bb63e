package com.ruoyi.web.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.domain.JiexiangApplication;
import org.apache.ibatis.annotations.Mapper;

/**
 * 结项信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
@Mapper
public interface JiexiangApplicationMapper extends BaseMapper<JiexiangApplication>
{
    /**
     * 查询结项信息
     * 
     * @param id 结项信息主键
     * @return 结项信息
     */
    public JiexiangApplication selectJiexiangApplicationById(Long id);

    /**
     * 查询结项信息列表
     * 
     * @param jiexiangApplication 结项信息
     * @return 结项信息集合
     */
    public List<JiexiangApplication> selectJiexiangApplicationList(JiexiangApplication jiexiangApplication);

    /**
     * 新增结项信息
     * 
     * @param jiexiangApplication 结项信息
     * @return 结果
     */
    public int insertJiexiangApplication(JiexiangApplication jiexiangApplication);

    /**
     * 修改结项信息
     * 
     * @param jiexiangApplication 结项信息
     * @return 结果
     */
    public int updateJiexiangApplication(JiexiangApplication jiexiangApplication);

    /**
     * 删除结项信息
     * 
     * @param id 结项信息主键
     * @return 结果
     */
    public int deleteJiexiangApplicationById(Long id);

    /**
     * 批量删除结项信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJiexiangApplicationByIds(Long[] ids);
}
