package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.Project2scoreRecordExportVO;
import com.ruoyi.web.service.IProject2scoreRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.web.service.IProject2scoreRuleDetailService;
import com.ruoyi.web.service.IProject2scoreRuleCategoryService;

/**
 * 积分记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/web/scoreRecord")
public class Project2scoreRecordController extends BaseController
{
    @Autowired
    private IProject2scoreRecordService project2scoreRecordService;
    
    @Autowired
    private ISysDeptService deptService;
    
    @Autowired
    private IProject2scoreRuleDetailService ruleDetailService;
    
    @Autowired
    private IProject2scoreRuleCategoryService ruleCategoryService;

    /**
     * 查询积分记录列表
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(Project2scoreRecord project2scoreRecord)
    {
        startPage();
        List<Project2scoreRecord> list = project2scoreRecordService.selectProject2scoreRecordList(project2scoreRecord);
        return getDataTable(list);
    }

    /**
     * 导出积分记录列表
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRecord:export')")
    @Log(title = "积分记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Project2scoreRecord project2scoreRecord)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 判断用户是否具有完全导出权限或是超级管理员
        boolean hasFullExportPermission = SecurityUtils.hasPermi("web:scoreCompletion") || loginUser.getUser().isAdmin();
        
        // 如果没有完全导出权限，则只能导出自己的数据
        if (!hasFullExportPermission) {
            project2scoreRecord.setUserId(SecurityUtils.getUserId());
        }
        
        // 如果没有选择时间范围，则按项目时间升序排序
        if (project2scoreRecord.getParams() == null) {
            project2scoreRecord.setParams(new HashMap<String, Object>());
        }
        if (project2scoreRecord.getParams().get("beginProjectTime") == null && 
            project2scoreRecord.getParams().get("endProjectTime") == null) {
            project2scoreRecord.getParams().put("orderBy", "r.project_time ASC");
        }
        
        List<Project2scoreRecordExportVO> list = project2scoreRecordService.selectExportList(project2scoreRecord);
        
        // 构建标题
        String beginTime = project2scoreRecord.getParams().get("beginProjectTime") != null 
            ? project2scoreRecord.getParams().get("beginProjectTime").toString() 
            : "";
        String endTime = project2scoreRecord.getParams().get("endProjectTime") != null 
            ? project2scoreRecord.getParams().get("endProjectTime").toString() 
            : "";
            
        // 构建完整标题
        StringBuilder titleBuilder = new StringBuilder();
        
        // 根据部门ID查询部门名称
        if (project2scoreRecord.getDeptId() != null) {
            String deptName = deptService.selectDeptById(project2scoreRecord.getDeptId()).getDeptName();
            if (StringUtils.isNotEmpty(deptName)) {
                titleBuilder.append("[").append(deptName).append("]");
            }
        }
        
        // 添加时间范围
        titleBuilder.append(StringUtils.isNotEmpty(beginTime) ? beginTime : "开始");
        titleBuilder.append("至");
        titleBuilder.append(StringUtils.isNotEmpty(endTime) ? endTime : "至今");
        
        // 根据规则分类ID查询分类名称
        if (project2scoreRecord.getCategoryId() != null) {
            String categoryName = ruleCategoryService.selectProject2scoreRuleCategoryById(project2scoreRecord.getCategoryId()).getCategoryName();
            if (StringUtils.isNotEmpty(categoryName)) {
                titleBuilder.append(" [").append(categoryName);
                // 如果同时选择了具体规则，则显示规则名称
                if (project2scoreRecord.getRuleId() != null) {
                    String ruleName = ruleDetailService.selectProject2scoreRuleDetailById(project2scoreRecord.getRuleId()).getRuleName();
                    if (StringUtils.isNotEmpty(ruleName)) {
                        titleBuilder.append("-").append(ruleName);
                    }
                }
                titleBuilder.append("]");
            }
        }
        // 如果只选择了规则没有选择分类
        else if (project2scoreRecord.getRuleId() != null) {
            String ruleName = ruleDetailService.selectProject2scoreRuleDetailById(project2scoreRecord.getRuleId()).getRuleName();
            if (StringUtils.isNotEmpty(ruleName)) {
                titleBuilder.append(" [").append(ruleName).append("]");
            }
        }
        
        titleBuilder.append("积分记录统计结果");
            
        ExcelUtil<Project2scoreRecordExportVO> util = new ExcelUtil<>(Project2scoreRecordExportVO.class);
        util.exportExcel(response, list, "积分记录", titleBuilder.toString());
    }

    /**
     * 获取积分记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        Project2scoreRecord record = project2scoreRecordService.selectProject2scoreRecordById(id);
        return success(record);
    }

    /**
     * 新增积分记录
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRecord:add')")
    @Log(title = "积分记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Project2scoreRecord project2scoreRecord)
    {
        return toAjax(project2scoreRecordService.insertProject2scoreRecord(project2scoreRecord));
    }

    /**
     * 修改积分记录
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRecord:edit')")
    @Log(title = "积分记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Project2scoreRecord project2scoreRecord)
    {
        return toAjax(project2scoreRecordService.updateProject2scoreRecord(project2scoreRecord));
    }

    /**
     * 删除积分记录
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRecord:remove')")
    @Log(title = "积分记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(project2scoreRecordService.deleteProject2scoreRecordByIds(ids));
    }
}
