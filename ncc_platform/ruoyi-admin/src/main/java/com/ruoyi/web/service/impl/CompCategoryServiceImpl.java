package com.ruoyi.web.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.CompCategoryMapper;
import com.ruoyi.web.domain.CompCategory;
import com.ruoyi.web.service.ICompCategoryService;

/**
 * 竞赛类别Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-22
 */
@Service
public class CompCategoryServiceImpl implements ICompCategoryService 
{
    @Autowired
    private CompCategoryMapper compCategoryMapper;

    @Autowired
    private ISysDictDataService dictDataService;


    /**
     * 查询竞赛类别
     * 
     * @param id 竞赛类别主键
     * @return 竞赛类别
     */
    @Override
    public CompCategory selectCompCategoryById(Long id)
    {
        return compCategoryMapper.selectCompCategoryById(id);
    }

    /**
     * 查询竞赛类别树状结构
     *
     * @param id 竞赛类别主键
     * @return 竞赛类别
     */
    @Override
    public List<CompCategory> selectCompCategoryList4Tree(CompCategory compCategory) {
        List<CompCategory> compCategories = compCategoryMapper.selectCompCategoryList(compCategory);
        ArrayList<CompCategory> result = new ArrayList<>();
        //循环遍历compCategories
        for (CompCategory temp : compCategories) {
            //如果temp 的 fatherid为0 则新增到result中
            if (temp.getFatherId() == 0) {
                result.add(temp);
            } else {
                //则在result中找到对应id与temp中fatherid相同的对象并将temp添加到children中
                for (CompCategory temp2 : result) {
                    if (temp2.getId() == temp.getFatherId()) {
                        if (temp2.getChildren() == null) {
                            ArrayList<CompCategory> child = new ArrayList<>();
                            child.add(temp);
                            temp2.setChildren(child);
                        } else {
                            List<CompCategory> children = temp2.getChildren();
                            children.add(temp);
                            temp2.setChildren(children);
                        }

                    }
                }
            }
        }
        return result;
    }

    /**
     * 查询竞赛类别列表
     * 
     * @param compCategory 竞赛类别
     * @return 竞赛类别
     */
    @Override
    public List<CompCategory> selectCompCategoryList(CompCategory compCategory)
    {
        return compCategoryMapper.selectCompCategoryList(compCategory);
    }

    /**
     * 新增竞赛类别
     * 
     * @param compCategory 竞赛类别
     * @return 结果
     */
    @Override
    public int insertCompCategory(CompCategory compCategory)
    {
        compCategory.setStatus(0L);
        //先插入到本表的数据库，之后在插入到字典的数据库中
        compCategoryMapper.insertCompCategory(compCategory);
        SysDictData dict = new SysDictData();
        dict.setDictType("comp_category");
        dict.setListClass("default");
        dict.setDictLabel(compCategory.getName());
        dict.setDictValue(compCategory.getId().toString());
        dict.setStatus("0");
        dict.setIsDefault("N");
        dict.setCreateBy("来自表自动");
        dict.setCreateTime(DateUtils.getNowDate());
        return dictDataService.insertDictData(dict);
    }

    /**
     * 修改竞赛类别
     * 
     * @param compCategory 竞赛类别
     * @return 结果
     */
    @Override
    public int updateCompCategory(CompCategory compCategory)
    {
        return compCategoryMapper.updateCompCategory(compCategory);
    }

    /**
     * 批量删除竞赛类别
     * 
     * @param ids 需要删除的竞赛类别主键
     * @return 结果
     */
    @Override
    public int deleteCompCategoryByIds(Long[] ids)
    {
        return compCategoryMapper.deleteCompCategoryByIds(ids);
    }

    /**
     * 删除竞赛类别信息
     * 
     * @param id 竞赛类别主键
     * @return 结果
     */
    @Override
    public int deleteCompCategoryById(Long id)
    {
        return compCategoryMapper.deleteCompCategoryById(id);
    }
}
