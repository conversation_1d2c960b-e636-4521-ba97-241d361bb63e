package com.ruoyi.web.mapper;



import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.web.domain.SysComQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 通用查询Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-08-18
 */
@Mapper
public interface SysComQueryMapper 
{
    /**
     * 查询通用查询
     * 
     * @param id 通用查询ID
     * @return 通用查询
     */
    public SysComQuery selectSysComQueryById(Long id);

    /**
     * 查询通用查询列表
     * 
     * @param sysComQuery 通用查询
     * @return 通用查询集合
     */
    public List<SysComQuery> selectSysComQueryList(SysComQuery sysComQuery);

    /**
     * 新增通用查询
     * 
     * @param sysComQuery 通用查询
     * @return 结果
     */
    public int insertSysComQuery(SysComQuery sysComQuery);

    /**
     * 修改通用查询
     * 
     * @param sysComQuery 通用查询
     * @return 结果
     */
    public int updateSysComQuery(SysComQuery sysComQuery);

    /**
     * 删除通用查询
     * 
     * @param id 通用查询ID
     * @return 结果
     */
    public int deleteSysComQueryById(Long id);

    /**
     * 批量删除通用查询
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysComQueryByIds(Long[] ids);
    
    public List<SysDictData> selectQueryData(SysComQuery sysComQuery);
}
