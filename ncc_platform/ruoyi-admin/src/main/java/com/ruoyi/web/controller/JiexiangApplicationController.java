package com.ruoyi.web.controller;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.ruoyi.common.config.RuoYiConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.ddr.poi.html.HtmlRenderPolicy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.JiexiangApplication;
import com.ruoyi.web.service.IJiexiangApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 结项信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
@Api(value = "/web/jiexiang", description = "结项信息")
@RestController
@RequestMapping("/web/jiexiang")
public class JiexiangApplicationController extends BaseController
{
    @Autowired
    private IJiexiangApplicationService jiexiangApplicationService;

    /**
     * 查询结项信息列表
     */
    @ApiOperation("查询结项信息列表")
    @PreAuthorize("@ss.hasPermi('web:jiexiang:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam(value = "查询结项信息参数", required = true) JiexiangApplication jiexiangApplication)
    {
        startPage();
        List<JiexiangApplication> list = jiexiangApplicationService.selectJiexiangApplicationList(jiexiangApplication);
        return getDataTable(list);
    }

    /**
     * 导出结项信息列表
     */
    @ApiOperation("导出结项信息列表")
    @PreAuthorize("@ss.hasPermi('web:jiexiang:export')")
    @Log(title = "结项信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@ApiParam(value = "导出结项信息参数",name = "jiexiangApplication", required = true)HttpServletResponse response, JiexiangApplication jiexiangApplication) throws IOException {
        //列表行循环插件
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        //HTML解析插件
        HtmlRenderPolicy htmlRenderPolicy = new HtmlRenderPolicy();
        Configure config = Configure.builder()
                .bind("指导老师", policy).bind("参赛选手", policy).bind("竞赛成果", policy)
                .bind("竞赛项目背景", htmlRenderPolicy).bind("竞赛项目实施过程", htmlRenderPolicy)
                .bind("竞赛项目参赛过程", htmlRenderPolicy).bind("竞赛项目所取得的成果", htmlRenderPolicy)
                .bind("竞赛项目现阶段存在的不足", htmlRenderPolicy).bind("今后对竞赛项目的展望等", htmlRenderPolicy)
                .build();

        Map<String, Object> dataList = jiexiangApplicationService.getDataList(jiexiangApplication);
        XWPFTemplate template = XWPFTemplate.compile(RuoYiConfig.getProfile() +"\\\\jiexiang.docx", config).render(dataList);
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition","attachment;filename=\""+"out_template.docx"+"\"");

        // HttpServletResponse response
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        template.write(bos);
        template.close();
        bos.flush();
        bos.close();
        out.flush();
        out.close();
    }

    /**
     * 获取结项信息详细信息
     */
    @ApiOperation("获取结项详情信息")
    @PreAuthorize("@ss.hasPermi('web:jiexiang:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(jiexiangApplicationService.selectJiexiangApplicationById(id));
    }

    /**
     * 新增结项信息
     */
    @ApiOperation("新增结项信息")
    @PreAuthorize("@ss.hasPermi('web:jiexiang:add')")
    @Log(title = "结项信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam(value = "新增结项信息参数",name = "jiexiangApplication", required = true)@RequestBody JiexiangApplication jiexiangApplication)
    {
        return toAjax(jiexiangApplicationService.insertJiexiangApplication(jiexiangApplication));
    }

    /**
     * 修改结项信息
     */
    @ApiOperation("修改结项信息")
    @PreAuthorize("@ss.hasPermi('web:jiexiang:edit')")
    @Log(title = "结项信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam(value = "修改结项信息参数",name = "jiexiangApplication", required = true)@RequestBody JiexiangApplication jiexiangApplication)
    {
        return toAjax(jiexiangApplicationService.updateJiexiangApplication(jiexiangApplication));
    }

    /**
     * 删除结项信息
     */
    @ApiOperation("删除结项信息")
    @PreAuthorize("@ss.hasPermi('web:jiexiang:remove')")
    @Log(title = "结项信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(jiexiangApplicationService.deleteJiexiangApplicationByIds(ids));
    }
}
