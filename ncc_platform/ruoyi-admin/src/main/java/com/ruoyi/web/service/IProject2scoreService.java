package com.ruoyi.web.service;

import java.util.List;
import com.ruoyi.web.domain.Project2score;
import com.ruoyi.web.domain.Project2scoreDistribution;
import com.ruoyi.web.domain.vo.Project2scoreExportVO;
import com.ruoyi.web.domain.vo.Project2scoreAuditExportVO;

/**
 * 项目积分Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IProject2scoreService 
{
    /**
     * 查询项目积分
     * 
     * @param id 项目积分主键
     * @return 项目积分
     */
    public Project2score selectProject2scoreById(Long id);

    /**
     * 查询项目积分列表
     * 
     * @param project2score 项目积分
     * @return 项目积分集合
     */
    public List<Project2score> selectProject2scoreList(Project2score project2score);

    /**
     * 查询当前用户的项目积分列表（按状态排序）
     * 
     * @param project2score 项目积分
     * @return 项目积分集合
     */
    public List<Project2score> selectMyProject2scoreList(Project2score project2score);

    /**
     * 新增项目积分
     * 
     * @param project2score 项目积分
     * @return 结果
     */
    public int insertProject2score(Project2score project2score);

    /**
     * 修改项目积分
     * 
     * @param project2score 项目积分
     * @return 结果
     */
    public int updateProject2score(Project2score project2score);

    /**
     * 批量删除项目积分
     * 
     * @param ids 需要删除的项目积分主键集合
     * @return 结果
     */
    public int deleteProject2scoreByIds(Long[] ids);

    /**
     * 删除项目积分信息
     * 
     * @param id 项目积分主键
     * @return 结果
     */
    public int deleteProject2scoreById(Long id);

    /**
     * 新增项目积分(包含团队成员)
     */
    public Long insertProject2scoreWithTeam(Project2score project, List<Project2scoreDistribution> distributions);

    /**
     * 修改项目积分(包含团队成员)
     */
    public int updateProject2scoreWithTeam(Project2score project, List<Project2scoreDistribution> distributions);

    /**
     * 查询项目积分列表（按状态排序：待审核->已驳回->已通过->草稿）
     * 
     * @param project2score 项目积分
     * @return 项目积分集合
     */
    public List<Project2score> selectProject2scoreAuditList(Project2score project2score);

    /**
     * 撤回项目积分审核
     * 
     * @param id 项目积分ID
     * @param userId 当前用户ID
     * @return 结果
     */
    public int revokeProject2score(Long id, Long userId);

    /**
     * 查询项目积分导出数据列表
     */
    public List<Project2scoreExportVO> selectProject2scoreExportList(Project2score project2score);

    /**
     * 查询项目积分审核导出列表
     * 
     * @param project2score 项目积分信息
     * @return 项目积分审核导出集合
     */
    public List<Project2scoreAuditExportVO> selectProject2scoreAuditExportList(Project2score project2score);
}
