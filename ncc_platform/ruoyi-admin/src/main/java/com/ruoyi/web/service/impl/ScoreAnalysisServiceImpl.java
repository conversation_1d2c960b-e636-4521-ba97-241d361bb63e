package com.ruoyi.web.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.web.domain.vo.ScoreAnalysisVO;
import com.ruoyi.web.domain.vo.ScoreTrendVO;
import com.ruoyi.web.domain.vo.ScoreTypeDistributionVO;
import com.ruoyi.web.domain.vo.DeptScoreAnalysisVO;
import com.ruoyi.web.mapper.ScoreAnalysisMapper;
import com.ruoyi.web.service.IScoreAnalysisService;

@Service
public class ScoreAnalysisServiceImpl implements IScoreAnalysisService {
    
    @Autowired
    private ScoreAnalysisMapper scoreAnalysisMapper;
    
    @Override
    public ScoreAnalysisVO getUserYearlyAnalysis(Integer year) {
        Long userId = SecurityUtils.getUserId();
        
        // 获取当年统计数据
        ScoreAnalysisVO currentYearStats = scoreAnalysisMapper.selectUserYearlyStats(userId, year);
        if (currentYearStats == null) {
            currentYearStats = new ScoreAnalysisVO();
            currentYearStats.setTotalScore(BigDecimal.ZERO);
            currentYearStats.setProjectCount(0L);
        }
        
        // 获取上年统计数据
        ScoreAnalysisVO lastYearStats = scoreAnalysisMapper.selectUserLastYearStats(userId, year);
        if (lastYearStats == null) {
            lastYearStats = new ScoreAnalysisVO();
            lastYearStats.setTotalScore(BigDecimal.ZERO);
            lastYearStats.setProjectCount(0L);
        }
        
        // 计算积分同比增长
        if (lastYearStats.getTotalScore() != null && lastYearStats.getTotalScore().compareTo(BigDecimal.ZERO) > 0) {
            // 如果去年有积分，计算增长率
            BigDecimal growth = currentYearStats.getTotalScore()
                .subtract(lastYearStats.getTotalScore())
                .multiply(new BigDecimal("100"))
                .divide(lastYearStats.getTotalScore(), 2, RoundingMode.HALF_UP);
            currentYearStats.setScoreGrowth(growth);
        } else if (currentYearStats.getTotalScore().compareTo(BigDecimal.ZERO) > 0) {
            // 如果去年无积分，今年有积分，增长率为100%
            currentYearStats.setScoreGrowth(new BigDecimal("100"));
        } else {
            // 如果都没有积分，增长率为0
            currentYearStats.setScoreGrowth(BigDecimal.ZERO);
        }
        
        // 计算项目数量同比增长
        if (lastYearStats.getProjectCount() != null && lastYearStats.getProjectCount() > 0) {
            // 如果去年有项目，计算增长率
            BigDecimal projectGrowth = BigDecimal.valueOf(currentYearStats.getProjectCount() - lastYearStats.getProjectCount())
                .multiply(new BigDecimal("100"))
                .divide(BigDecimal.valueOf(lastYearStats.getProjectCount()), 2, RoundingMode.HALF_UP);
            currentYearStats.setProjectGrowth(projectGrowth);
        } else if (currentYearStats.getProjectCount() > 0) {
            // 如果去年无项目，今年有项目，增长率为100%
            currentYearStats.setProjectGrowth(new BigDecimal("100"));
        } else {
            // 如果都没有项目，增长率为0
            currentYearStats.setProjectGrowth(BigDecimal.ZERO);
        }
        
        // 计算排名变化
        if (lastYearStats.getSchoolRank() != null && currentYearStats.getSchoolRank() != null) {
            // 排名变化 = 今年排名 - 上年排名（正数表示排名下降，负数表示排名上升）
            currentYearStats.setRankChange(currentYearStats.getSchoolRank() - lastYearStats.getSchoolRank());
        } else if (currentYearStats.getSchoolRank() != null) {
            // 如果去年无排名，今年有排名，说明是新增用户
            currentYearStats.setRankChange(0L);
        }
        
        // 计算部门排名变化
        if (lastYearStats.getDeptRank() != null && currentYearStats.getDeptRank() != null) {
            // 部门排名变化 = 今年排名 - 上年排名
            currentYearStats.setDeptRankChange(currentYearStats.getDeptRank() - lastYearStats.getDeptRank());
        } else if (currentYearStats.getDeptRank() != null) {
            // 如果去年无排名，今年有排名，说明是新增用户
            currentYearStats.setDeptRankChange(0L);
        }
        
        return currentYearStats;
    }
    
    @Override
    public List<ScoreTrendVO> getUserScoreTrend(Integer year, String timeUnit) {
        Long userId = SecurityUtils.getUserId();
        
        if ("quarter".equals(timeUnit)) {
            return scoreAnalysisMapper.selectUserQuarterlyTrend(userId, year);
        } else {
            return scoreAnalysisMapper.selectUserMonthlyTrend(userId, year);
        }
    }
    
    @Override
    public List<ScoreTypeDistributionVO> getUserProjectTypeDistribution(Integer year) {
        Long userId = SecurityUtils.getUserId();
        return scoreAnalysisMapper.selectUserProjectTypeDistribution(userId, year);
    }
    
    @Override
    public DeptScoreAnalysisVO getDeptYearlyAnalysis(Long deptId, Integer year) {
        // 获取当年统计数据
        DeptScoreAnalysisVO currentYearStats = scoreAnalysisMapper.selectDeptYearlyStats(deptId, year);
        if (currentYearStats == null) {
            currentYearStats = new DeptScoreAnalysisVO();
            currentYearStats.setTotalScore(BigDecimal.ZERO);
            currentYearStats.setAvgScore(BigDecimal.ZERO);
            currentYearStats.setProjectCount(0L);
        }
        
        // 获取上年统计数据
        DeptScoreAnalysisVO lastYearStats = scoreAnalysisMapper.selectDeptLastYearStats(deptId, year);
        if (lastYearStats == null) {
            lastYearStats = new DeptScoreAnalysisVO();
            lastYearStats.setTotalScore(BigDecimal.ZERO);
            lastYearStats.setAvgScore(BigDecimal.ZERO);
            lastYearStats.setProjectCount(0L);
        }
        
        // 计算总积分同比增长
        if (lastYearStats.getTotalScore().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal growth = currentYearStats.getTotalScore()
                .subtract(lastYearStats.getTotalScore())
                .multiply(new BigDecimal("100"))
                .divide(lastYearStats.getTotalScore(), 2, RoundingMode.HALF_UP);
            currentYearStats.setScoreGrowth(growth);
        } else if (currentYearStats.getTotalScore().compareTo(BigDecimal.ZERO) > 0) {
            currentYearStats.setScoreGrowth(new BigDecimal("100"));
        } else {
            currentYearStats.setScoreGrowth(BigDecimal.ZERO);
        }
        
        // 计算人均积分同比增长
        if (lastYearStats.getAvgScore().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal avgGrowth = currentYearStats.getAvgScore()
                .subtract(lastYearStats.getAvgScore())
                .multiply(new BigDecimal("100"))
                .divide(lastYearStats.getAvgScore(), 2, RoundingMode.HALF_UP);
            currentYearStats.setAvgScoreGrowth(avgGrowth);
        } else if (currentYearStats.getAvgScore().compareTo(BigDecimal.ZERO) > 0) {
            currentYearStats.setAvgScoreGrowth(new BigDecimal("100"));
        } else {
            currentYearStats.setAvgScoreGrowth(BigDecimal.ZERO);
        }
        
        // 计算项目数量同比增长
        if (lastYearStats.getProjectCount() > 0) {
            BigDecimal projectGrowth = BigDecimal.valueOf(currentYearStats.getProjectCount() - lastYearStats.getProjectCount())
                .multiply(new BigDecimal("100"))
                .divide(BigDecimal.valueOf(lastYearStats.getProjectCount()), 2, RoundingMode.HALF_UP);
            currentYearStats.setProjectGrowth(projectGrowth);
        } else if (currentYearStats.getProjectCount() > 0) {
            currentYearStats.setProjectGrowth(new BigDecimal("100"));
        } else {
            currentYearStats.setProjectGrowth(BigDecimal.ZERO);
        }
        
        // 计算排名变化
        if (lastYearStats.getRank() != null && currentYearStats.getRank() != null) {
            currentYearStats.setRankChange(lastYearStats.getRank() - currentYearStats.getRank());
        } else if (currentYearStats.getRank() != null) {
            currentYearStats.setRankChange(0L);
        }
        
        return currentYearStats;
    }
    
    @Override
    public List<ScoreTrendVO> getDeptScoreTrend(Long deptId, Integer year, String timeUnit) {
        if ("quarter".equals(timeUnit)) {
            return scoreAnalysisMapper.selectDeptQuarterlyTrend(deptId, year);
        } else {
            return scoreAnalysisMapper.selectDeptMonthlyTrend(deptId, year);
        }
    }
    
    @Override
    public List<ScoreTrendVO> getDeptAvgScoreTrend(Long deptId, Integer year) {
        return scoreAnalysisMapper.selectDeptAvgScoreTrend(deptId, year);
    }
    
    @Override
    public List<ScoreTrendVO> getDeptRankTrend(Long deptId, Integer year) {
        return scoreAnalysisMapper.selectDeptRankTrend(deptId, year);
    }
    
    @Override
    public List<ScoreTypeDistributionVO> getDeptProjectTypeDistribution(Long deptId, Integer year) {
        return scoreAnalysisMapper.selectDeptProjectTypeDistribution(deptId, year);
    }
} 