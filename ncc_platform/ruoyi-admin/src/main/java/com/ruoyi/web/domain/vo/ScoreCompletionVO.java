package com.ruoyi.web.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 积分完成情况对象 ScoreCompletionVO
 * 
 * <AUTHOR>
 */
public class ScoreCompletionVO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 教师工号 */
    @Excel(name = "教师工号")
    private String userName;

    /** 教师姓名 */
    @Excel(name = "教师姓名")
    private String nickName;

    /** 部门ID */
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 角色ID集合，逗号分隔 */
    private String roleIds;

    /** 角色名称集合，逗号分隔 */
    @Excel(name = "角色")
    private String roleNames;

    /** 总积分 */
    @Excel(name = "当前积分")
    private BigDecimal totalScore;

    /** 目标积分 */
    @Excel(name = "目标积分")
    private BigDecimal targetScore;

    /** 完成情况 (0未完成 1已完成 2超额完成) */
    @Excel(name = "完成情况", readConverterExp = "-1=未匹配,0=未完成,1=已完成,2=超额完成")
    private String completionStatus;

    /** 最近积分时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最近积分日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastScoreTime;

    /** 职务 */
    @Excel(name = "职务")
    private String job;

    /** 职称 */
    @Excel(name = "职称")
    private String ranks;

    /** 工号 */
    @Excel(name = "工号")
    private String no;

   

    /**
     * 规则ID
     */
    private Long ruleId;


    /**
     * 完成率
     */
    @Excel(name = "完成率")
    private Double completionRatio;


    /**
     * 超额完成比例 (百分比)
     */
    @Excel(name = "超额完成比例")
    private Double excessCompletionRatio;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(String roleIds) {
        this.roleIds = roleIds;
    }

    public String getRoleNames() {
        return roleNames;
    }

    public void setRoleNames(String roleNames) {
        this.roleNames = roleNames;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public BigDecimal getTargetScore() {
        return targetScore;
    }

    public void setTargetScore(BigDecimal targetScore) {
        this.targetScore = targetScore;
    }

    public String getCompletionStatus() {
        return completionStatus;
    }

    public void setCompletionStatus(String completionStatus) {
        this.completionStatus = completionStatus;
    }

    public Date getLastScoreTime() {
        return lastScoreTime;
    }

    public void setLastScoreTime(Date lastScoreTime) {
        this.lastScoreTime = lastScoreTime;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getRanks() {
        return ranks;
    }

    public void setRanks(String ranks) {
        this.ranks = ranks;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public Double getExcessCompletionRatio() {
        return excessCompletionRatio;
    }

    public void setExcessCompletionRatio(Double excessCompletionRatio) {
        this.excessCompletionRatio = excessCompletionRatio;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public Double getCompletionRatio() {
        return completionRatio;
    }

    public void setCompletionRatio(Double completionRatio) {
        this.completionRatio = completionRatio;
    }

} 