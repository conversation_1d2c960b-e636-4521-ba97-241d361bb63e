package com.ruoyi.web.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.domain.LixiangApplication;
import org.apache.ibatis.annotations.Mapper;

/**
 * 立项信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Mapper
public interface LixiangApplicationMapper extends BaseMapper<LixiangApplication>
{
    /**
     * 查询立项信息
     * 
     * @param id 立项信息主键
     * @return 立项信息
     */
    public LixiangApplication selectLixiangApplicationById(Long id);

    /**
     * 查询立项信息列表
     * 
     * @param lixiangApplication 立项信息
     * @return 立项信息集合
     */
    public List<LixiangApplication> selectLixiangApplicationList(LixiangApplication lixiangApplication);

    /**
     * 新增立项信息
     * 
     * @param lixiangApplication 立项信息
     * @return 结果
     */
    public int insertLixiangApplication(LixiangApplication lixiangApplication);

    /**
     * 修改立项信息
     * 
     * @param lixiangApplication 立项信息
     * @return 结果
     */
    public int updateLixiangApplication(LixiangApplication lixiangApplication);

    /**
     * 删除立项信息
     * 
     * @param id 立项信息主键
     * @return 结果
     */
    public int deleteLixiangApplicationById(Long id);

    /**
     * 批量删除立项信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLixiangApplicationByIds(Long[] ids);
}
