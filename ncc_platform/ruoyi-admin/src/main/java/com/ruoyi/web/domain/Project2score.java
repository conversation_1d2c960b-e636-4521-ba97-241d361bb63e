package com.ruoyi.web.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 项目积分对象 project2score
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class Project2score extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long id;

    /** 申请人ID */
    @Excel(name = "申请人ID")
    private Long userId;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 规则ID */
    @Excel(name = "规则ID")
    private Long ruleId;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 申请名称 */
    @Excel(name = "申请名称")
    private String applyName;

    /** 项目时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date projectTime;

    /** 金额（万元） */
    @Excel(name = "金额", readConverterExp = "万=元")
    private BigDecimal amount;

    /** 计算得出的积分 */
    @Excel(name = "计算得出的积分")
    private BigDecimal score;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String filePath;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 是否为团队项目 */
    @Excel(name = "是否为团队项目")
    private String isTeam;

    @TableField(exist = false)
    private String nickName;

    /** 用于搜索的用户名/昵称 */
    @TableField(exist = false)
    private String searchUserName;
    
    /** 部门ID列表（非数据库字段，用于权限过滤） */
    @TableField(exist = false)
    private List<Long> deptIds;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }
    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }
    public void setApplyName(String applyName) 
    {
        this.applyName = applyName;
    }

    public String getApplyName() 
    {
        return applyName;
    }
    public void setProjectTime(Date projectTime) 
    {
        this.projectTime = projectTime;
    }

    public Date getProjectTime() 
    {
        return projectTime;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }
    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }
    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }
    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setIsTeam(String isTeam) 
    {
        this.isTeam = isTeam;
    }

    public String getIsTeam() 
    {
        return isTeam;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getSearchUserName() {
        return searchUserName;
    }

    public void setSearchUserName(String searchUserName) {
        this.searchUserName = searchUserName;
        this.nickName = searchUserName; // 同时设置nickName，用于SQL查询
    }

    public List<Long> getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(List<Long> deptIds) {
        this.deptIds = deptIds;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("deptId", getDeptId())
            .append("ruleId", getRuleId())
            .append("applyName", getApplyName())
            .append("projectTime", getProjectTime())
            .append("amount", getAmount())
            .append("score", getScore())
            .append("fileName", getFileName())
            .append("filePath", getFilePath())
            .append("remark", getRemark())
            .append("status", getStatus())
            .append("isTeam", getIsTeam())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
