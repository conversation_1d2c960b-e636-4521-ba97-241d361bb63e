package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.system.service.ISysPostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.CompetitionBaseInfo;
import com.ruoyi.web.service.ICompetitionBaseInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 竞赛信息基础信息Controller
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Api(value = "/web/competitionBaseInfo", tags = "竞赛信息基础信息")
@RestController
@RequestMapping("/web/competitionBaseInfo")
public class CompetitionBaseInfoController extends BaseController
{
    @Autowired
    private ICompetitionBaseInfoService competitionBaseInfoService;

    /**
     * 查询竞赛信息基础信息列表
     */
    @ApiOperation("查询竞赛信息基础信息列表")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam(value = "查询竞赛信息基础信息参数", required = true) CompetitionBaseInfo competitionBaseInfo)
    {
        startPage();
        List<CompetitionBaseInfo> list = competitionBaseInfoService.selectCompetitionBaseInfoList(competitionBaseInfo,getLoginUser());
        return getDataTable(list);
    }

    /**
     * 导出竞赛信息基础信息列表
     */
    @ApiOperation("导出竞赛信息基础信息列表")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:export')")
    @Log(title = "竞赛信息基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@ApiParam(value = "导出竞赛信息基础信息参数", required = true) HttpServletResponse response, CompetitionBaseInfo competitionBaseInfo)
    {
        List<CompetitionBaseInfo> list = competitionBaseInfoService.selectCompetitionBaseInfoList(competitionBaseInfo,getLoginUser());
        ExcelUtil<CompetitionBaseInfo> util = new ExcelUtil<CompetitionBaseInfo>(CompetitionBaseInfo.class);
        util.exportExcel(response, list, "竞赛信息");
    }

    /**
     * 获取竞赛信息基础信息详细信息
     */
    @ApiOperation("获取竞赛信息基础信息详细信息")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(value = "竞赛信息基础信息ID", required = true)@PathVariable("id") Long id)
    {
        return success(competitionBaseInfoService.selectCompetitionBaseInfoById(id));
    }

    /**
     * 新增竞赛信息基础信息
     */
    @ApiOperation("新增竞赛信息基础信息")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:add')")
    @Log(title = "竞赛信息基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam(value = "竞赛信息基础信息参数", required = true)@RequestBody CompetitionBaseInfo competitionBaseInfo)
    {
        competitionBaseInfo.setCreateUserId(getUserId());
        competitionBaseInfo.setCreateUserDept(getDeptId());
        return toAjax(competitionBaseInfoService.insertCompetitionBaseInfo(competitionBaseInfo));
    }

    /**
     * 修改竞赛信息基础信息
     */
    @ApiOperation("修改竞赛信息基础信息")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:edit')")
    @Log(title = "竞赛信息基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam(value = "竞赛信息基础信息参数", required = true)@RequestBody CompetitionBaseInfo competitionBaseInfo)
    {
        return toAjax(competitionBaseInfoService.updateCompetitionBaseInfo(competitionBaseInfo));
    }

    /**
     * 删除竞赛信息基础信息
     */
    @ApiOperation("删除竞赛信息基础信息")
    @PreAuthorize("@ss.hasPermi('web:competitionBaseInfo:remove')")
    @Log(title = "竞赛信息基础信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam(value = "竞赛信息基础信息ID数组", required = true)@PathVariable Long[] ids)
    {
        return toAjax(competitionBaseInfoService.deleteCompetitionBaseInfoByIds(ids));
    }
}
