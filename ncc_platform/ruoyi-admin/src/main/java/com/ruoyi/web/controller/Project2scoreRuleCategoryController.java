package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.Project2scoreRuleCategory;
import com.ruoyi.web.service.IProject2scoreRuleCategoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 积分规则分类Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/web/scoreRuleCategory")
public class Project2scoreRuleCategoryController extends BaseController
{
    @Autowired
    private IProject2scoreRuleCategoryService project2scoreRuleCategoryService;

    /**
     * 查询积分规则分类列表
     */
    @GetMapping("/list")
    public AjaxResult list(Project2scoreRuleCategory project2scoreRuleCategory)
    {
        List<Project2scoreRuleCategory> list = project2scoreRuleCategoryService.selectProject2scoreRuleCategoryList(project2scoreRuleCategory);
        return success(list);
    }

    /**
     * 导出积分规则分类列表
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRuleCategory:export')")
    @Log(title = "积分规则分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Project2scoreRuleCategory project2scoreRuleCategory)
    {
        List<Project2scoreRuleCategory> list = project2scoreRuleCategoryService.selectProject2scoreRuleCategoryList(project2scoreRuleCategory);
        ExcelUtil<Project2scoreRuleCategory> util = new ExcelUtil<Project2scoreRuleCategory>(Project2scoreRuleCategory.class);
        util.exportExcel(response, list, "积分规则分类数据");
    }

    /**
     * 获取积分规则分类详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(project2scoreRuleCategoryService.selectProject2scoreRuleCategoryById(id));
    }

    /**
     * 新增积分规则分类
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRuleCategory:add')")
    @Log(title = "积分规则分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Project2scoreRuleCategory project2scoreRuleCategory)
    {
        return toAjax(project2scoreRuleCategoryService.insertProject2scoreRuleCategory(project2scoreRuleCategory));
    }

    /**
     * 修改积分规则分类
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRuleCategory:edit')")
    @Log(title = "积分规则分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Project2scoreRuleCategory project2scoreRuleCategory)
    {
        return toAjax(project2scoreRuleCategoryService.updateProject2scoreRuleCategory(project2scoreRuleCategory));
    }

    /**
     * 删除积分规则分类
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRuleCategory:remove')")
    @Log(title = "积分规则分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(project2scoreRuleCategoryService.deleteProject2scoreRuleCategoryByIds(ids));
    }

    /**
     * 获取积分规则分类树状结构
     */
    @GetMapping("/tree/{id}")
    public AjaxResult getTree(@PathVariable(required = false) Long id)
    {
        List<Project2scoreRuleCategory> list;
        if (id != null) {
            list = project2scoreRuleCategoryService.selectKidsProject2scoreRuleCategoryById(id);
        } else {
            Project2scoreRuleCategory category = new Project2scoreRuleCategory();
            list = project2scoreRuleCategoryService.selectProject2scoreRuleCategoryList(category);
        }
        return success(project2scoreRuleCategoryService.buildTree(list));
    }

    /**
     * 获取完整树状结构
     */
    @GetMapping("/tree")
    public AjaxResult getFullTree()
    {
        Project2scoreRuleCategory category = new Project2scoreRuleCategory();
        List<Project2scoreRuleCategory> list = project2scoreRuleCategoryService.selectProject2scoreRuleCategoryList(category);
        return success(project2scoreRuleCategoryService.buildTree(list));
    }
}
