package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.Project2scoreCompletionRule;
import com.ruoyi.web.service.IProject2scoreCompletionRuleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 科研指标规则Controller
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
@RestController
@RequestMapping("/web/completionRule")
public class Project2scoreCompletionRuleController extends BaseController
{
    @Autowired
    private IProject2scoreCompletionRuleService project2scoreCompletionRuleService;

    /**
     * 查询科研指标规则列表
     */
    @PreAuthorize("@ss.hasPermi('web:completionRule:list')")
    @GetMapping("/list")
    public TableDataInfo list(Project2scoreCompletionRule project2scoreCompletionRule)
    {
        startPage();
        List<Project2scoreCompletionRule> list = project2scoreCompletionRuleService.selectProject2scoreCompletionRuleList(project2scoreCompletionRule);
        return getDataTable(list);
    }

    /**
     * 导出科研指标规则列表
     */
    @PreAuthorize("@ss.hasPermi('web:completionRule:export')")
    @Log(title = "科研指标规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Project2scoreCompletionRule project2scoreCompletionRule)
    {
        List<Project2scoreCompletionRule> list = project2scoreCompletionRuleService.selectProject2scoreCompletionRuleList(project2scoreCompletionRule);
        ExcelUtil<Project2scoreCompletionRule> util = new ExcelUtil<Project2scoreCompletionRule>(Project2scoreCompletionRule.class);
        util.exportExcel(response, list, "科研指标规则数据");
    }

    /**
     * 获取科研指标规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('web:completionRule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(project2scoreCompletionRuleService.selectProject2scoreCompletionRuleById(id));
    }

    /**
     * 新增科研指标规则
     */
    @PreAuthorize("@ss.hasPermi('web:completionRule:add')")
    @Log(title = "科研指标规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Project2scoreCompletionRule project2scoreCompletionRule)
    {
        return toAjax(project2scoreCompletionRuleService.insertProject2scoreCompletionRule(project2scoreCompletionRule));
    }

    /**
     * 修改科研指标规则
     */
    @PreAuthorize("@ss.hasPermi('web:completionRule:edit')")
    @Log(title = "科研指标规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Project2scoreCompletionRule project2scoreCompletionRule)
    {
        return toAjax(project2scoreCompletionRuleService.updateProject2scoreCompletionRule(project2scoreCompletionRule));
    }

    /**
     * 删除科研指标规则
     */
    @PreAuthorize("@ss.hasPermi('web:completionRule:remove')")
    @Log(title = "科研指标规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(project2scoreCompletionRuleService.deleteProject2scoreCompletionRuleByIds(ids));
    }
}
