package com.ruoyi.web.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目积分审核导出对象
 */
public class Project2scoreAuditExportVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 申请人 */
    @Excel(name = "申请人")
    private String nickName;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String applyName;

    /** 积分规则 */
    @Excel(name = "积分规则")
    private String ruleName;

    /** 项目时间 */
    @Excel(name = "项目时间", width = 30, dateFormat = "yyyy-MM-dd")
    private String projectTime;

    /** 项目总积分 */
    @Excel(name = "项目总积分")
    private String score;

    /** 是否团队项目 */
    @Excel(name = "是否团队项目", readConverterExp = "0=否,1=是")
    private String isTeam;

    /** 团队成员积分分配 */
    @Excel(name = "团队成员积分分配")
    private String teamMemberScores;

    /** 项目状态 */
    @Excel(name = "项目状态", readConverterExp = "0=草稿,1=待审核,2=已通过,3=已驳回")
    private String status;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditUserName;

    /** 审核状态 */
    @Excel(name = "审核状态", readConverterExp = "0=通过,1=驳回")
    private String auditStatus;

    /** 审核建议 */
    @Excel(name = "审核建议")
    private String auditRemark;

    /** 审核时间 */
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String auditTime;

    // Getters and Setters
    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getProjectTime() {
        return projectTime;
    }

    public void setProjectTime(String projectTime) {
        this.projectTime = projectTime;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getIsTeam() {
        return isTeam;
    }

    public void setIsTeam(String isTeam) {
        this.isTeam = isTeam;
    }

    public String getTeamMemberScores() {
        return teamMemberScores;
    }

    public void setTeamMemberScores(String teamMemberScores) {
        this.teamMemberScores = teamMemberScores;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAuditUserName() {
        return auditUserName;
    }

    public void setAuditUserName(String auditUserName) {
        this.auditUserName = auditUserName;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }
} 