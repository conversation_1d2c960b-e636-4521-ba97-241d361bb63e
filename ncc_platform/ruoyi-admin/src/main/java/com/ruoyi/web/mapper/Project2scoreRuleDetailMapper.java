package com.ruoyi.web.mapper;

import java.util.List;
import com.ruoyi.web.domain.Project2scoreRuleDetail;
import org.apache.ibatis.annotations.Param;

/**
 * 积分规则详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface Project2scoreRuleDetailMapper 
{
    /**
     * 查询积分规则详情
     * 
     * @param id 积分规则详情主键
     * @return 积分规则详情
     */
    public Project2scoreRuleDetail selectProject2scoreRuleDetailById(Long id);

    /**
     * 查询积分规则详情列表
     * 
     * @param project2scoreRuleDetail 积分规则详情
     * @return 积分规则详情集合
     */
    public List<Project2scoreRuleDetail> selectProject2scoreRuleDetailList(Project2scoreRuleDetail project2scoreRuleDetail);

    /**
     * 新增积分规则详情
     * 
     * @param project2scoreRuleDetail 积分规则详情
     * @return 结果
     */
    public int insertProject2scoreRuleDetail(Project2scoreRuleDetail project2scoreRuleDetail);

    /**
     * 修改积分规则详情
     * 
     * @param project2scoreRuleDetail 积分规则详情
     * @return 结果
     */
    public int updateProject2scoreRuleDetail(Project2scoreRuleDetail project2scoreRuleDetail);

    /**
     * 删除积分规则详情
     * 
     * @param id 积分规则详情主键
     * @return 结果
     */
    public int deleteProject2scoreRuleDetailById(Long id);

    /**
     * 批量删除积分规则详情
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProject2scoreRuleDetailByIds(Long[] ids);

    /**
     * 根据分类ID更新规则详情状态
     *
     * @param categoryId 分类ID
     * @param status 状态
     * @return 结果
     */
    public int updateStatusByCategoryId(@Param("categoryId") Long categoryId, @Param("status") String status);
}
