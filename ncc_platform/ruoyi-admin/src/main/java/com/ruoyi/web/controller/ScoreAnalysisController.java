package com.ruoyi.web.controller;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.web.domain.vo.ScoreAnalysisVO;
import com.ruoyi.web.domain.vo.ScoreTrendVO;
import com.ruoyi.web.domain.vo.ScoreTypeDistributionVO;
import com.ruoyi.web.domain.vo.DeptScoreAnalysisVO;
import com.ruoyi.web.service.IScoreAnalysisService;

@RestController
@RequestMapping("/web/score/analysis")
public class ScoreAnalysisController extends BaseController {
    
    @Autowired
    private IScoreAnalysisService scoreAnalysisService;
    
    /**
     * 获取用户年度积分分析数据
     */
    @GetMapping("/yearly")
    public AjaxResult getYearlyAnalysis(@RequestParam(required = false) Integer year) {
        if (year == null) {
            year = java.time.Year.now().getValue();
        }
        ScoreAnalysisVO data = scoreAnalysisService.getUserYearlyAnalysis(year);
        return AjaxResult.success(data);
    }
    
    /**
     * 获取用户积分趋势数据
     */
    @GetMapping("/trend")
    public AjaxResult getScoreTrend(
            @RequestParam(required = false) Integer year,
            @RequestParam(defaultValue = "month") String timeUnit) {
        if (year == null) {
            year = java.time.Year.now().getValue();
        }
        List<ScoreTrendVO> data = scoreAnalysisService.getUserScoreTrend(year, timeUnit);
        return AjaxResult.success(data);
    }
    
    /**
     * 获取用户项目类型分布
     */
    @GetMapping("/type-distribution")
    public AjaxResult getTypeDistribution(@RequestParam(required = false) Integer year) {
        if (year == null) {
            year = java.time.Year.now().getValue();
        }
        List<ScoreTypeDistributionVO> data = scoreAnalysisService.getUserProjectTypeDistribution(year);
        return AjaxResult.success(data);
    }
    
    /**
     * 获取部门年度积分分析数据
     */
    @GetMapping("/dept/yearly")
    public AjaxResult getDeptYearlyAnalysis(
            @RequestParam Long deptId,
            @RequestParam(required = false) Integer year) {
        if (year == null) {
            year = java.time.Year.now().getValue();
        }
        DeptScoreAnalysisVO data = scoreAnalysisService.getDeptYearlyAnalysis(deptId, year);
        return AjaxResult.success(data);
    }
    
    /**
     * 获取部门积分趋势数据
     */
    @GetMapping("/dept/trend")
    public AjaxResult getDeptScoreTrend(
            @RequestParam Long deptId,
            @RequestParam(required = false) Integer year,
            @RequestParam(defaultValue = "month") String timeUnit) {
        if (year == null) {
            year = java.time.Year.now().getValue();
        }
        List<ScoreTrendVO> data = scoreAnalysisService.getDeptScoreTrend(deptId, year, timeUnit);
        return AjaxResult.success(data);
    }
    
    /**
     * 获取部门人均积分趋势数据
     */
    @GetMapping("/dept/avg-trend")
    public AjaxResult getDeptAvgScoreTrend(
            @RequestParam Long deptId,
            @RequestParam(required = false) Integer year) {
        if (year == null) {
            year = java.time.Year.now().getValue();
        }
        List<ScoreTrendVO> data = scoreAnalysisService.getDeptAvgScoreTrend(deptId, year);
        return AjaxResult.success(data);
    }
    
    /**
     * 获取部门排名趋势数据
     */
    @GetMapping("/dept/rank-trend")
    public AjaxResult getDeptRankTrend(
            @RequestParam Long deptId,
            @RequestParam(required = false) Integer year) {
        if (year == null) {
            year = java.time.Year.now().getValue();
        }
        List<ScoreTrendVO> data = scoreAnalysisService.getDeptRankTrend(deptId, year);
        return AjaxResult.success(data);
    }
    
    /**
     * 获取部门项目类型分布
     */
    @GetMapping("/dept/type-distribution")
    public AjaxResult getDeptTypeDistribution(
            @RequestParam Long deptId,
            @RequestParam(required = false) Integer year) {
        if (year == null) {
            year = java.time.Year.now().getValue();
        }
        List<ScoreTypeDistributionVO> data = scoreAnalysisService.getDeptProjectTypeDistribution(deptId, year);
        return AjaxResult.success(data);
    }
} 