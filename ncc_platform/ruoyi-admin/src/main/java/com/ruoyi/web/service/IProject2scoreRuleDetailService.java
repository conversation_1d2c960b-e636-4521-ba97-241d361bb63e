package com.ruoyi.web.service;

import java.util.List;
import com.ruoyi.web.domain.Project2scoreRuleDetail;

/**
 * 积分规则详情Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IProject2scoreRuleDetailService 
{
    /**
     * 查询积分规则详情
     * 
     * @param id 积分规则详情主键
     * @return 积分规则详情
     */
    public Project2scoreRuleDetail selectProject2scoreRuleDetailById(Long id);

    /**
     * 查询积分规则详情列表
     * 
     * @param project2scoreRuleDetail 积分规则详情
     * @return 积分规则详情集合
     */
    public List<Project2scoreRuleDetail> selectProject2scoreRuleDetailList(Project2scoreRuleDetail project2scoreRuleDetail);

    /**
     * 新增积分规则详情
     * 
     * @param project2scoreRuleDetail 积分规则详情
     * @return 结果
     */
    public int insertProject2scoreRuleDetail(Project2scoreRuleDetail project2scoreRuleDetail);

    /**
     * 修改积分规则详情
     * 
     * @param project2scoreRuleDetail 积分规则详情
     * @return 结果
     */
    public int updateProject2scoreRuleDetail(Project2scoreRuleDetail project2scoreRuleDetail);

    /**
     * 批量删除积分规则详情
     * 
     * @param ids 需要删除的积分规则详情主键集合
     * @return 结果
     */
    public int deleteProject2scoreRuleDetailByIds(Long[] ids);

    /**
     * 删除积分规则详情信息
     * 
     * @param id 积分规则详情主键
     * @return 结果
     */
    public int deleteProject2scoreRuleDetailById(Long id);
}
