package com.ruoyi.web.mapper;

import java.util.List;
import com.ruoyi.web.domain.Project2scoreRuleCategory;

/**
 * 积分规则分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface Project2scoreRuleCategoryMapper 
{
    /**
     * 查询积分规则分类
     * 
     * @param id 积分规则分类主键
     * @return 积分规则分类
     */
    public Project2scoreRuleCategory selectProject2scoreRuleCategoryById(Long id);

    /**
     * 查询积分规则分类列表
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 积分规则分类集合
     */
    public List<Project2scoreRuleCategory> selectProject2scoreRuleCategoryList(Project2scoreRuleCategory project2scoreRuleCategory);

    /**
     * 新增积分规则分类
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 结果
     */
    public int insertProject2scoreRuleCategory(Project2scoreRuleCategory project2scoreRuleCategory);

    /**
     * 修改积分规则分类
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 结果
     */
    public int updateProject2scoreRuleCategory(Project2scoreRuleCategory project2scoreRuleCategory);

    /**
     * 删除积分规则分类
     * 
     * @param id 积分规则分类主键
     * @return 结果
     */
    public int deleteProject2scoreRuleCategoryById(Long id);

    /**
     * 批量删除积分规则分类
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProject2scoreRuleCategoryByIds(Long[] ids);

    /**
     * 根据祖级列表查询子节点
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 积分规则分类集合
     */
    public List<Project2scoreRuleCategory> selectChildrenByAncestors(Project2scoreRuleCategory project2scoreRuleCategory);

    /**
     * 根据ancestors批量更新子节点状态
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 结果
     */
    public int updateChildrenStatus(Project2scoreRuleCategory project2scoreRuleCategory);
}
