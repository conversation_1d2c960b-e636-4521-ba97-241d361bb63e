package com.ruoyi.web.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目积分导出对象
 */
public class Project2scoreExportVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String applyName;

    /** 积分规则 */
    @Excel(name = "积分规则")
    private String ruleName;

    /** 项目时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date projectTime;

    /** 项目总积分 */
    @Excel(name = "项目总积分")
    private BigDecimal score;

    /** 是否团队项目 */
    @Excel(name = "是否团队项目", readConverterExp = "0=否,1=是")
    private String isTeam;

    /** 团队成员积分分配 */
    @Excel(name = "团队成员积分分配")
    private String teamMemberScores;

    /** 项目状态 */
    @Excel(name = "项目状态", readConverterExp = "0=草稿,1=待审核,2=已通过,3=已驳回")
    private String status;

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public Date getProjectTime() {
        return projectTime;
    }

    public void setProjectTime(Date projectTime) {
        this.projectTime = projectTime;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public String getIsTeam() {
        return isTeam;
    }

    public void setIsTeam(String isTeam) {
        this.isTeam = isTeam;
    }

    public String getTeamMemberScores() {
        return teamMemberScores;
    }

    public void setTeamMemberScores(String teamMemberScores) {
        this.teamMemberScores = teamMemberScores;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
} 