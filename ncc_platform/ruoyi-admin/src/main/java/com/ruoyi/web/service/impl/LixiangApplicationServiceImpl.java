package com.ruoyi.web.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.date.DateUtil;
import com.deepoove.poi.XWPFTemplate;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.domain.*;
import com.ruoyi.web.service.ICompContestantService;
import com.ruoyi.web.service.ICompInstructorService;
import com.ruoyi.web.service.ICompetitionBaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.LixiangApplicationMapper;
import com.ruoyi.web.service.ILixiangApplicationService;

/**
 * 立项信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
public class LixiangApplicationServiceImpl implements ILixiangApplicationService 
{
    @Autowired
    private LixiangApplicationMapper lixiangApplicationMapper;

    @Autowired
    private ICompetitionBaseInfoService  contestBaseInfoService;

    @Autowired
    private ICompInstructorService compInstructorService;

    @Autowired
    private ICompContestantService compContestantService;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询立项信息
     * 
     * @param id 立项信息主键
     * @return 立项信息
     */
    @Override
    public LixiangApplication selectLixiangApplicationById(Long id)
    {
        return lixiangApplicationMapper.selectLixiangApplicationById(id);
    }

    /**
     * 查询立项信息列表
     * 
     * @param lixiangApplication 立项信息
     * @return 立项信息
     */
    @Override
    public List<LixiangApplication> selectLixiangApplicationList(LixiangApplication lixiangApplication)
    {
        return lixiangApplicationMapper.selectLixiangApplicationList(lixiangApplication);
    }

    /**
     * 新增立项信息
     * 
     * @param lixiangApplication 立项信息
     * @return 结果
     */
    @Override
    public int insertLixiangApplication(LixiangApplication lixiangApplication)
    {
        lixiangApplication.setCreateTime(DateUtils.getNowDate());
        return lixiangApplicationMapper.insertLixiangApplication(lixiangApplication);
    }

    /**
     * 修改立项信息
     * 
     * @param lixiangApplication 立项信息
     * @return 结果
     */
    @Override
    public int updateLixiangApplication(LixiangApplication lixiangApplication)
    {
        lixiangApplication.setUpdateTime(DateUtils.getNowDate());
        return lixiangApplicationMapper.updateLixiangApplication(lixiangApplication);
    }

    /**
     * 批量删除立项信息
     * 
     * @param ids 需要删除的立项信息主键
     * @return 结果
     */
    @Override
    public int deleteLixiangApplicationByIds(Long[] ids)
    {
        return lixiangApplicationMapper.deleteLixiangApplicationByIds(ids);
    }

    /**
     * 删除立项信息信息
     * 
     * @param id 立项信息主键
     * @return 结果
     */
    @Override
    public int deleteLixiangApplicationById(Long id)
    {
        return lixiangApplicationMapper.deleteLixiangApplicationById(id);
    }

    /**
     * 获取模板
     *
     * @param lixiangApplication
     * @return
     */
    @Override
    public Map<String, Object> getDataList(LixiangApplication lixiangApplication) {
        //根据竞赛ID获取竞赛信息
        CompetitionBaseInfo competitionBaseInfo = contestBaseInfoService.selectCompetitionBaseInfoById(lixiangApplication.getCompetitionId());
        //根据竞赛信息中创建人id获取创建人信息
        SysUser sysUser = userService.selectUserById(competitionBaseInfo.getCreateUserId());
        //根据竞赛ID获取竞赛指导老师信息
        CompInstructor compInstructor = new CompInstructor();
        compInstructor.setCompId(lixiangApplication.getCompetitionId());
        List<CompInstructor> compInstructors = compInstructorService.selectCompInstructorList(compInstructor);
        //根据竞赛ID获取竞赛参赛成员信息
        CompContestantVO compContestant = new CompContestantVO();
        compContestant.setCompetitionId(lixiangApplication.getCompetitionId());
        List<CompContestantVO> compContestants = compContestantService.selectCompContestantListPlus(compContestant);
        //根据竞赛id获取立项书信息
        List<LixiangApplication> lixiangApplications = lixiangApplicationMapper.selectLixiangApplicationList(lixiangApplication);
        if (lixiangApplications.size() > 0){
            lixiangApplication = lixiangApplications.get(0);
        }else {
            throw new RuntimeException("未找到立项书信息");
        }
        Map<String, Object> data = new HashMap<>();
        //竞赛基础信息内容填写
        data.put("项目名称",competitionBaseInfo.getName());
        data.put("赛项名称",competitionBaseInfo.getEventName());
        data.put("项目负责人",sysUser.getNickName());
        data.put("联系电话",sysUser.getPhonenumber());
        data.put("竞赛主办单位",competitionBaseInfo.getOrganizer());
        data.put("竞赛承办单位",competitionBaseInfo.getUndertaker());
        data.put("申报日期", DateUtil.format(new Date(), "yyyy年 MM月 dd日"));
        data.put("竞赛时间", DateUtil.format(competitionBaseInfo.getMatchTime(), "yyyy年 MM月 dd日"));
        data.put("竞赛地点", competitionBaseInfo.getMatchVenue());
        //处理竞赛级别
        switch (competitionBaseInfo.getLevel()){
            case 6:
                data.put("竞赛级别","√国际级     □国家级     □省级     □市级   □校级  ");
                break;
            case 5:
                data.put("竞赛级别","□国际级     √国家级     □省级     □市级   □校级  ");
                break;
            case 3:
                data.put("竞赛级别","□国际级     □国家级     √省级     □市级   □校级  ");
                break;
            case 2:
                data.put("竞赛级别","□国际级     □国家级     □省级     √市级   □校级  ");
                break;
            case 0:
                data.put("竞赛级别","□国际级     □国家级     □省级     □市级   √校级  ");
                break;
        }
        //处理主办单位类型
        switch (competitionBaseInfo.getOrganizationalType()){
            case "0":
                data.put("组织单位类型1","√政府行政部门                □教学指导委员会（政府部门下属事业单位）");
                data.put("组织单位类型2","□企业、行业协会");
                break;
            case "1":
                data.put("组织单位类型1","□政府行政部门                √教学指导委员会（政府部门下属事业单位）");
                data.put("组织单位类型2","□企业、行业协会");
                break;
            case "2":
                data.put("组织单位类型1","□政府行政部门                □教学指导委员会（政府部门下属事业单位）");
                data.put("组织单位类型2","√企业、行业协会");
                break;
        }
        //处理指导老师信息
        data.put("指导老师",compInstructors);
        //处理参赛选手信息
        // 遍历compContestants处理班级名称为 年级+专业+班级
        for (CompContestantVO contestant : compContestants) {
            //获取学号前两位作为年级
            String grade = contestant.getNo().substring(0, 2);
            String className = grade+ "级" + contestant.getMajor() + contestant.getClassName();
            contestant.setClassName(className);
        }
        data.put("参赛选手",compContestants);
        //竞赛项目简介
        data.put("竞赛信息",lixiangApplication.getCompetitionInfo());
        data.put("参赛情况",lixiangApplication.getCompetitionHistory());
        data.put("竞赛内容",lixiangApplication.getCompetitionContent());
        //竞赛实施方案
        data.put("宣传方案",lixiangApplication.getCompetitionPromotionPlan());
        data.put("组织方式",lixiangApplication.getCompetitionOrganizational());
        data.put("竞赛培训",lixiangApplication.getCompetitionTrainingPlan());
        //经费预算表
        data.put("研发人员劳务",lixiangApplication.getCost0());
        data.put("管理人员劳务",lixiangApplication.getCost1());
        data.put("耗材费",lixiangApplication.getCost2());
        data.put("仪器设备使用费",lixiangApplication.getCost3());
        data.put("专用设备购置",lixiangApplication.getCost4());
        data.put("实验室改造",lixiangApplication.getCost5());
        data.put("实验材料费",lixiangApplication.getCost6());
        data.put("测试及试验费",lixiangApplication.getCost7());
        data.put("学生参与活动的学生交通费",lixiangApplication.getCost8());
        data.put("教师差旅费",lixiangApplication.getCost9());
        data.put("非图书资料费",lixiangApplication.getCost10());
        data.put("出版费",lixiangApplication.getCost11());
        data.put("咨询",lixiangApplication.getCost12());
        data.put("会议费",lixiangApplication.getCost13());
        data.put("图书",lixiangApplication.getCost14());
        data.put("办公费",lixiangApplication.getCost15());
        data.put("学生竞赛费用",lixiangApplication.getCost16());
        data.put("报名考试",lixiangApplication.getCost17());
        data.put("其他费用1金额",lixiangApplication.getCost18());
        data.put("其他费用2金额",lixiangApplication.getCost19());
        data.put("其他费用3金额",lixiangApplication.getCost20());
        data.put("其他费用1",lixiangApplication.getCost18Name());
        data.put("其他费用2",lixiangApplication.getCost19Name());
        data.put("其他费用3",lixiangApplication.getCost20Name());
        data.put("经费支出合计",lixiangApplication.getCost21());
        return data;
    }
}
