package com.ruoyi.web.service.impl;

import java.util.List;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.ScoreCompletionMapper;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.ScoreCompletionVO;
import com.ruoyi.web.service.IScoreCompletionService;
import com.ruoyi.web.domain.Project2scoreCompletionRule;
import com.ruoyi.web.service.IProject2scoreCompletionRuleService;
import java.math.BigDecimal;

/**
 * 积分完成情况Service实现
 *
 * <AUTHOR>
 */
@Service
public class ScoreCompletionServiceImpl implements IScoreCompletionService
{
    @Autowired
    private ScoreCompletionMapper scoreCompletionMapper;

    @Autowired
    private IProject2scoreCompletionRuleService ruleService;

    /**
     * 查询积分完成情况列表
     *
     * @param scoreCompletion 查询参数
     * @return 积分完成情况
     */
    @Override
    public List<ScoreCompletionVO> selectScoreCompletionList(ScoreCompletionVO scoreCompletion)
    {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("deptId", scoreCompletion.getDeptId());
        params.put("userName", scoreCompletion.getUserName());
        params.put("nickName", scoreCompletion.getNickName());
        params.put("roleIds", scoreCompletion.getRoleIds());
        params.put("job", scoreCompletion.getJob());
        params.put("ranks", scoreCompletion.getRanks());
        params.put("no", scoreCompletion.getNo());
        params.put("completionStatus", scoreCompletion.getCompletionStatus());
        params.put("excessCompletionRatio", scoreCompletion.getExcessCompletionRatio());

        // 处理查询日期范围
        if (scoreCompletion.getParams() != null) {
            params.put("beginTime", scoreCompletion.getParams().get("beginTime"));
            params.put("endTime", scoreCompletion.getParams().get("endTime"));
        }

        // 获取积分完成情况列表
        List<ScoreCompletionVO> completionList = scoreCompletionMapper.selectScoreCompletionList(params);

        // 只有当列表不为空时，才进行规则匹配和指标完成情况分析
        if (completionList != null && !completionList.isEmpty()) {
            // 获取所有科研指标规则
            Project2scoreCompletionRule queryRule = new Project2scoreCompletionRule();
            List<Project2scoreCompletionRule> allRules = ruleService.selectProject2scoreCompletionRuleList(queryRule);

            // 遍历每个用户
            for (ScoreCompletionVO user : completionList) {
                // 匹配规则
                Project2scoreCompletionRule matchedRule = matchRuleForUser(user, allRules);
                if (matchedRule != null) { // 如果匹配到规则
                    // 设置规则ID
                    user.setRuleId(matchedRule.getId());

                    // 设置目标积分
                    user.setTargetScore(matchedRule.getTarget());

                    // 防御性编程：确保积分不为空
                    BigDecimal totalScore = user.getTotalScore() != null ? user.getTotalScore() : BigDecimal.ZERO;
                    BigDecimal targetScore = matchedRule.getTarget() != null ? matchedRule.getTarget() : BigDecimal.ONE;

                    // 计算原始完成率（当前积分/目标积分）
                    double rawCompletionRatio = totalScore.divide(targetScore, 2, BigDecimal.ROUND_HALF_UP).doubleValue();

                    // 设置完成率（上限为100%）
                    double completionRatio = rawCompletionRatio > 1.0 ? 1.0 : rawCompletionRatio;
                    user.setCompletionRatio(completionRatio);

                    // 设置超额完成比例 (实际积分-目标积分)/目标积分
                    BigDecimal excessAmount = totalScore.subtract(targetScore);
                    double excessRatio = excessAmount.divide(targetScore, 2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    user.setExcessCompletionRatio(excessRatio);

                    // 设置完成状态（0未完成 1已完成 2超额完成）
                    int comparison = totalScore.compareTo(targetScore);
                    if (comparison < 0) {
                        // 未完成
                        user.setCompletionStatus("0");
                    } else if (comparison == 0) {
                        // 刚好完成
                        user.setCompletionStatus("1");
                    } else {
                        // 超额完成
                        user.setCompletionStatus("2");
                    }
                } else {
                    // 未匹配到规则，设置默认值
                    user.setCompletionStatus("-1"); // 默认未匹配
                    user.setCompletionRatio(0.0);  // 默认完成率为0
                    user.setExcessCompletionRatio(0.0); // 默认超额完成比例为0
                }
            }

            // 根据完成情况进行筛选
            if (scoreCompletion.getCompletionStatus() != null && !"".equals(scoreCompletion.getCompletionStatus())) {
                // 如果用户的完成情况与查询条件不符，则从列表中移除
                completionList.removeIf(user -> !scoreCompletion.getCompletionStatus().equals(user.getCompletionStatus()));
            }
        }

        return completionList;
    }

    /**
     * 匹配规则
     * @param user 用户
     * @param allRules 所有规则
     * @return 匹配的规则
     */
    private Project2scoreCompletionRule matchRuleForUser(ScoreCompletionVO user, List<Project2scoreCompletionRule> allRules) {
        // 防御性编程：检查参数
        if (user == null || allRules == null || allRules.isEmpty()) {
            return null;
        }

        // 提取用户职务和职称，避免重复调用
        String userJob = user.getJob();
        String userRanks = user.getRanks();

        // 初始化匹配结果变量
        Project2scoreCompletionRule jobMatch = null;       // 仅职务匹配
        Project2scoreCompletionRule ranksMatch = null;     // 仅职称匹配

        // 遍历所有规则
        for (Project2scoreCompletionRule rule : allRules) {
            // 跳过状态为停用(1)的规则
            if ("1".equals(rule.getStatus())) {
                continue;
            }

            // 防御性编程：检查规则属性是否为空
            boolean jobMatches = userJob != null && userJob.equals(rule.getJob());
            boolean ranksMatches = userRanks != null && userRanks.equals(rule.getRanks());

            // 情况1：职务和职称都匹配 - 最高优先级，立即返回
            if (jobMatches && ranksMatches) {
                return rule;
            }

            // 记录部分匹配情况，区分职务匹配和职称匹配
            if (jobMatches && jobMatch == null) {
                jobMatch = rule;    // 记录首个职务匹配
            }

            if (ranksMatches && ranksMatch == null) {
                ranksMatch = rule;  // 记录首个职称匹配
            }
        }

        // 情况2：部分匹配，按优先级返回（这里优先考虑职务匹配，可根据业务需求调整）
        if (jobMatch != null) {
            return jobMatch;        // 优先返回职务匹配
        }

        if (ranksMatch != null) {
            return ranksMatch;      // 其次返回职称匹配
        }

        // 情况3：没有任何匹配，返回null
        return null;
    }

    /**
     * 查询用户积分明细列表
     *
     * @param userId 用户ID
     * @param record 查询参数
     * @return 积分记录集合
     */
    @Override
    public List<Project2scoreRecord> selectScoreDetailsList(Long userId, Project2scoreRecord record)
    {
        Map<String, Object> params = new HashMap<>();
        if (record.getParams() != null) {
            params.put("beginTime", record.getParams().get("beginTime"));
            params.put("endTime", record.getParams().get("endTime"));
        }
        return scoreCompletionMapper.selectScoreDetailsList(userId, params);
    }

    /**
     * 统计用户积分明细总数
     *
     * @param userId 用户ID
     * @param record 查询参数
     * @return 总记录数
     */
    @Override
    public int countScoreDetails(Long userId, Project2scoreRecord record)
    {
        Map<String, Object> params = new HashMap<>();
        if (record.getParams() != null) {
            params.put("beginTime", record.getParams().get("beginTime"));
            params.put("endTime", record.getParams().get("endTime"));
        }
        return scoreCompletionMapper.countScoreDetails(userId, params);
    }
}