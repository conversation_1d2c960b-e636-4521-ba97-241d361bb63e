package com.ruoyi.web.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.ArrayList;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.Project2scoreRuleCategoryMapper;
import com.ruoyi.web.domain.Project2scoreRuleCategory;
import com.ruoyi.web.service.IProject2scoreRuleCategoryService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.web.mapper.Project2scoreRuleDetailMapper;
import com.ruoyi.web.domain.Project2scoreRuleDetail;

/**
 * 积分规则分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class Project2scoreRuleCategoryServiceImpl implements IProject2scoreRuleCategoryService 
{
    @Autowired
    private Project2scoreRuleCategoryMapper project2scoreRuleCategoryMapper;

    @Autowired
    private Project2scoreRuleDetailMapper project2scoreRuleDetailMapper;

    /**
     * 查询积分规则分类
     * 
     * @param id 积分规则分类主键
     * @return 积分规则分类
     */
    @Override
    public Project2scoreRuleCategory selectProject2scoreRuleCategoryById(Long id)
    {
        return project2scoreRuleCategoryMapper.selectProject2scoreRuleCategoryById(id);
    }

    /**
     * 查询积分规则分类列表
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 积分规则分类
     */
    @Override
    public List<Project2scoreRuleCategory> selectProject2scoreRuleCategoryList(Project2scoreRuleCategory project2scoreRuleCategory)
    {
        return project2scoreRuleCategoryMapper.selectProject2scoreRuleCategoryList(project2scoreRuleCategory);
    }

    /**
     * 新增积分规则分类
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 结果
     */
    @Override
    public int insertProject2scoreRuleCategory(Project2scoreRuleCategory project2scoreRuleCategory)
    {
        project2scoreRuleCategory.setCreateTime(DateUtils.getNowDate());
        
        // 如果父节点不为空，设置ancestors
        if (project2scoreRuleCategory.getParentId() != null && project2scoreRuleCategory.getParentId() != 0L) {
            Project2scoreRuleCategory parent = project2scoreRuleCategoryMapper.selectProject2scoreRuleCategoryById(project2scoreRuleCategory.getParentId());
            if (parent != null) {
                // 设置ancestors，格式为: 父节点的ancestors + 父节点ID +
                project2scoreRuleCategory.setAncestors(parent.getAncestors() + "," + parent.getId());
            }
        } else {
            // 如果是顶级节点，ancestors为0
            project2scoreRuleCategory.setAncestors("0");
        }
        
        return project2scoreRuleCategoryMapper.insertProject2scoreRuleCategory(project2scoreRuleCategory);
    }

    /**
     * 修改积分规则分类
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 结果
     */
    @Override
    public int updateProject2scoreRuleCategory(Project2scoreRuleCategory project2scoreRuleCategory)
    {
        project2scoreRuleCategory.setUpdateTime(DateUtils.getNowDate());
        
        // 如果状态发生变化，同步更新子节点状态
        if (project2scoreRuleCategory.getStatus() != null) {
            // 批量更新所有子节点状态
            project2scoreRuleCategoryMapper.updateChildrenStatus(project2scoreRuleCategory);
            
            // 同步更新当前分类下的规则详情状态
            project2scoreRuleDetailMapper.updateStatusByCategoryId(project2scoreRuleCategory.getId(), project2scoreRuleCategory.getStatus());
            
            // 获取所有子节点
            Project2scoreRuleCategory query = new Project2scoreRuleCategory();
            query.setAncestors(project2scoreRuleCategory.getId().toString());
            List<Project2scoreRuleCategory> children = project2scoreRuleCategoryMapper.selectChildrenByAncestors(query);
            
            // 更新所有子节点下的规则详情状态
            for (Project2scoreRuleCategory child : children) {
                project2scoreRuleDetailMapper.updateStatusByCategoryId(child.getId(), project2scoreRuleCategory.getStatus());
            }
        }
        
        return project2scoreRuleCategoryMapper.updateProject2scoreRuleCategory(project2scoreRuleCategory);
    }

    /**
     * 批量删除积分规则分类
     * 
     * @param ids 需要删除的积分规则分类主键集合
     * @return 结果
     */
    @Override
    public int deleteProject2scoreRuleCategoryByIds(Long[] ids)
    {
        for (Long id : ids) {
            // 1. 检查是否存在子节点
            Project2scoreRuleCategory query = new Project2scoreRuleCategory();
            query.setParentId(id);
            List<Project2scoreRuleCategory> children = project2scoreRuleCategoryMapper.selectProject2scoreRuleCategoryList(query);
            if (!children.isEmpty()) {
                Project2scoreRuleCategory category = selectProject2scoreRuleCategoryById(id);
                throw new ServiceException(String.format("%1$s存在子节点,不允许删除", category.getCategoryName()));
            }
            
            // 2. 检查是否存在关联的规则详情
            Project2scoreRuleDetail ruleQuery = new Project2scoreRuleDetail();
            ruleQuery.setCategoryId(id);
            List<Project2scoreRuleDetail> rules = project2scoreRuleDetailMapper.selectProject2scoreRuleDetailList(ruleQuery);
            if (!rules.isEmpty()) {
                Project2scoreRuleCategory category = selectProject2scoreRuleCategoryById(id);
                throw new ServiceException(String.format("%1$s存在关联的规则详情,不允许删除", category.getCategoryName()));
            }
        }
        return project2scoreRuleCategoryMapper.deleteProject2scoreRuleCategoryByIds(ids);
    }

    /**
     * 删除积分规则分类信息
     * 
     * @param id 积分规则分类主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreRuleCategoryById(Long id)
    {
        // 1. 检查是否存在子节点
        Project2scoreRuleCategory query = new Project2scoreRuleCategory();
        query.setParentId(id);
        List<Project2scoreRuleCategory> children = project2scoreRuleCategoryMapper.selectProject2scoreRuleCategoryList(query);
        if (!children.isEmpty()) {
            throw new ServiceException("存在子节点,不允许删除");
        }
        
        // 2. 检查是否存在关联的规则详情
        Project2scoreRuleDetail ruleQuery = new Project2scoreRuleDetail();
        ruleQuery.setCategoryId(id);
        List<Project2scoreRuleDetail> rules = project2scoreRuleDetailMapper.selectProject2scoreRuleDetailList(ruleQuery);
        if (!rules.isEmpty()) {
            throw new ServiceException("存在关联的规则详情,不允许删除");
        }
        
        return project2scoreRuleCategoryMapper.deleteProject2scoreRuleCategoryById(id);
    }

    /**
     * 查找所有子节点
     *
     * @param id 积分规则分类主键
     * @return 结果
     */
    @Override
    public List<Project2scoreRuleCategory> selectKidsProject2scoreRuleCategoryById(Long id) {
        Project2scoreRuleCategory query = new Project2scoreRuleCategory();
        query.setAncestors(id.toString());
        return project2scoreRuleCategoryMapper.selectChildrenByAncestors(query);
    }

    /**
     * 构建树
     */
    public List<Project2scoreRuleCategory> buildTree(List<Project2scoreRuleCategory> categories) {
        List<Project2scoreRuleCategory> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        
        for (Project2scoreRuleCategory category : categories) {
            tempList.add(category.getId());
        }

        for (Project2scoreRuleCategory category : categories) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(category.getParentId())) {
                recursionFn(categories, category);
                returnList.add(category);
            }
        }

        if (returnList.isEmpty()) {
            returnList = categories;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<Project2scoreRuleCategory> list, Project2scoreRuleCategory t) {
        // 得到子节点列表
        List<Project2scoreRuleCategory> childList = getChildList(list, t);
        t.setChildren(childList);
        for (Project2scoreRuleCategory tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<Project2scoreRuleCategory> getChildList(List<Project2scoreRuleCategory> list, Project2scoreRuleCategory t) {
        List<Project2scoreRuleCategory> tlist = new ArrayList<>();
        for (Project2scoreRuleCategory n : list) {
            if (n.getParentId() != null && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        // 添加排序
        tlist.sort((a, b) -> {
            Long sortA = a.getCategorySort() != null ? a.getCategorySort() : 0L;
            Long sortB = b.getCategorySort() != null ? b.getCategorySort() : 0L;
            return sortA.compareTo(sortB);
        });
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<Project2scoreRuleCategory> list, Project2scoreRuleCategory t) {
        return getChildList(list, t).size() > 0;
    }
}
