package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 竞赛选手对象 comp_contestant
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Data
@TableName(value = "comp_contestant")
@ApiModel(value = "竞赛选手对象", description = "竞赛选手对象")
public class CompContestantVO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Excel(name = "主键")
    @ApiModelProperty(value = "主键", required = true)
    private Long id;

    private String competitionName;
    /** 比赛ID */
    @Excel(name = "比赛ID")
    @ApiModelProperty(value = "比赛ID", required = true)
    private Long competitionId;

    /** 选手ID */
    @Excel(name = "选手ID")
    @ApiModelProperty(value = "选手ID", required = true)
    private String contestantId;


    /** 所获奖项(字典获取也可以手输) */
    @Excel(name = "所获奖项",dictType="awards")
    @ApiModelProperty(value = "所获奖项", required = true)
    private String awards;

    /** 选手类型(0:学生,1:老师) */
    @Excel(name = "选手类型",dictType= "contestant_type")
    @ApiModelProperty(value = "选手类型", required = true)
    private Integer type;

    /** 学生姓名 */
    @Excel(name = "姓名")
    @ApiModelProperty(value = "学生姓名", required = true)
    private String name;

    /** 学生专业 */
    @Excel(name = "专业")
    @ApiModelProperty(value = "学生专业", required = true)
    private String major;

    /** 学生班级 */
    @Excel(name = "班级")
    @ApiModelProperty(value = "学生班级", required = true)
    private String className;

    /** 教研室 */
    @Excel(name = "教研室")
    @ApiModelProperty(value = "教研室", required = true)
    private String deptName;

    /** 工号 */
    @Excel(name = "工号")
    @ApiModelProperty(value = "工号", required = true)
    private String no;

    /** 选手角色 */
    @Excel(name = "职责及分工")
    @ApiModelProperty(value = "选手角色", required = true)
    private String contestantRole;
    /** 性别 */
    @Excel(name = "性别", dictType = "sys_user_sex" ,combo={"男","女"})
    @ApiModelProperty(value = "性别", required = true)
    private Integer gender;

    @Excel(name = "队伍")
    @ApiModelProperty(value = "队伍", required = true)
    private String group;
}
