package com.ruoyi.web.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.web.domain.JiexiangApplication;
import com.ruoyi.web.domain.LixiangApplication;

/**
 * 结项信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
public interface IJiexiangApplicationService 
{
    /**
     * 查询结项信息
     * 
     * @param id 结项信息主键
     * @return 结项信息
     */
    public JiexiangApplication selectJiexiangApplicationById(Long id);

    /**
     * 查询结项信息列表
     * 
     * @param jiexiangApplication 结项信息
     * @return 结项信息集合
     */
    public List<JiexiangApplication> selectJiexiangApplicationList(JiexiangApplication jiexiangApplication);

    /**
     * 新增结项信息
     * 
     * @param jiexiangApplication 结项信息
     * @return 结果
     */
    public int insertJiexiangApplication(JiexiangApplication jiexiangApplication);

    /**
     * 修改结项信息
     * 
     * @param jiexiangApplication 结项信息
     * @return 结果
     */
    public int updateJiexiangApplication(JiexiangApplication jiexiangApplication);

    /**
     * 批量删除结项信息
     * 
     * @param ids 需要删除的结项信息主键集合
     * @return 结果
     */
    public int deleteJiexiangApplicationByIds(Long[] ids);

    /**
     * 删除结项信息信息
     * 
     * @param id 结项信息主键
     * @return 结果
     */
    public int deleteJiexiangApplicationById(Long id);

    /**
     * 获取模板
     * @param jiexiangApplication
     * @return
     */
    public Map<String, Object> getDataList(JiexiangApplication jiexiangApplication);
}
