package com.ruoyi.web.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.domain.CompContestant;
import com.ruoyi.web.domain.CompContestantVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 竞赛选手Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Mapper
public interface CompContestantMapper extends BaseMapper<CompContestant>
{
    /**
     * 查询竞赛选手
     * 
     * @param id 竞赛选手主键
     * @return 竞赛选手
     */
    public CompContestant selectCompContestantById(Long id);

    /**
     * 查询竞赛选手列表
     * 
     * @param compContestant 竞赛选手
     * @return 竞赛选手集合
     */
    public List<CompContestant> selectCompContestantList(CompContestant compContestant);
    /**
     * 查询竞赛选手列表
     *
     * @param compContestant 竞赛选手
     * @return 竞赛选手集合
     */
    public List<CompContestantVO> selectCompContestantListPlus(CompContestantVO compContestant);

    /**
     * 新增竞赛选手
     * 
     * @param compContestant 竞赛选手
     * @return 结果
     */
    public int insertCompContestant(CompContestant compContestant);

    /**
     * 修改竞赛选手
     * 
     * @param compContestant 竞赛选手
     * @return 结果
     */
    public int updateCompContestant(CompContestant compContestant);

    /**
     * 删除竞赛选手
     * 
     * @param id 竞赛选手主键
     * @return 结果
     */
    public int deleteCompContestantById(Long id);

    /**
     * 批量删除竞赛选手
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompContestantByIds(Long[] ids);
}
