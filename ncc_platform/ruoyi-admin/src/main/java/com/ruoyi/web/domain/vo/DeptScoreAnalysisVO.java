package com.ruoyi.web.domain.vo;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class DeptScoreAnalysisVO {
    /** 年度总积分 */
    private BigDecimal totalScore;
    
    /** 积分增长率 */
    private BigDecimal scoreGrowth;
    
    /** 人均积分 */
    private BigDecimal avgScore;
    
    /** 人均积分增长率 */
    private BigDecimal avgScoreGrowth;
    
    /** 部门排名 */
    private Long rank;
    
    /** 总部门数 */
    private Long totalDepts;
    
    /** 排名变化 */
    private Long rankChange;
    
    /** 项目数量 */
    private Long projectCount;
    
    /** 项目数量增长率 */
    private BigDecimal projectGrowth;
} 