package com.ruoyi.web.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.annotation.Excel.Type;

/**
 * 积分记录导出对象
 * 
 * <AUTHOR>
 */
public class Project2scoreRecordExportVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 教师名 */
    @Excel(name = "教师名", sort = 1, needMerge = true)
    private String nickName;

    /** 部门名称 */
    @Excel(name = "部门名称", width = 30, sort = 2, needMerge = true)
    private String deptName;

    /** 项目名称 */
    @Excel(name = "项目名称", width = 50, sort = 3, needMerge = true)
    private String projectName;

    /** 项目时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目时间", width = 30, dateFormat = "yyyy-MM-dd", sort = 4, needMerge = true)
    private Date projectTime;

    /** 项目类型 */
    @Excel(name = "项目类型", readConverterExp = "0=个人,1=团队", sort = 5, needMerge = true)
    private String isTeam;

    /** 是否为团队负责人 */
    @Excel(name = "是否为团队负责人", readConverterExp = "0=否,1=是", sort = 6, needMerge = true)
    private String isLeader;

    /** 积分规则 */
    @Excel(name = "积分规则", width = 80, sort = 7, needMerge = true)
    private String ruleName;

    /** 个人积分值 */
    @Excel(name = "个人积分值", sort = 8, isStatistics = true)
    private BigDecimal score;

    /** 备注 */
    @Excel(name = "备注", width = 100, sort = 9, needMerge = true)
    private String remark;

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Date getProjectTime() {
        return projectTime;
    }

    public void setProjectTime(Date projectTime) {
        this.projectTime = projectTime;
    }

    public String getIsTeam() {
        return isTeam;
    }

    public void setIsTeam(String isTeam) {
        this.isTeam = isTeam;
    }

    public String getIsLeader() {
        return isLeader;
    }

    public void setIsLeader(String isLeader) {
        this.isLeader = isLeader;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 创建合计行
     * @param totalScore 总积分
     * @return 合计行对象
     */
    public static Project2scoreRecordExportVO createTotalRow(BigDecimal totalScore) {
        Project2scoreRecordExportVO total = new Project2scoreRecordExportVO();
        total.setNickName("合计");
        total.setScore(totalScore);
        return total;
    }
}