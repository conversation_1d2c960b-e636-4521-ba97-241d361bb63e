package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.web.domain.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.service.ICompContestantService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 竞赛选手Controller
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Api(value = "/web/compContestant", tags = "竞赛选手")
@RestController
@RequestMapping("/web/compContestant")
public class CompContestantController extends BaseController
{
    @Autowired
    private ICompContestantService compContestantService;

    /**
     * 查询竞赛选手列表
     */
    @ApiOperation("查询竞赛选手列表")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam(value = "查询竞赛选手参数", required = true) CompContestant compContestant)
    {
        startPage();
        CompContestantVO compContestantVO = new CompContestantVO();
        BeanUtil.copyProperties(compContestant,compContestantVO);
        List<CompContestantVO> list = compContestantService.selectCompContestantListPro(compContestantVO);
        return getDataTable(list);
    }

    /**
     * 查询竞赛选手列表增强版（查询到选手信息）
     */
    @ApiOperation("查询竞赛选手列表增强版（查询到选手信息）")
    @GetMapping("/listPlus")
    public TableDataInfo listPlus(@ApiParam(value = "查询竞赛选手参数", required = true) CompContestantVO compContestant)
    {
        startPage();
        List<CompContestantVO> list = compContestantService.selectCompContestantListPlus(compContestant);
        return getDataTable(list);
    }

    /**
     * 导出竞赛选手列表
     */
    @ApiOperation("导出竞赛选手列表")
    @Log(title = "竞赛选手", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@ApiParam(value = "导出竞赛选手参数", required = true) HttpServletResponse response, CompContestant compContestant)
    {
        List<CompContestant> list = compContestantService.selectCompContestantList(compContestant);
        ExcelUtil<CompContestant> util = new ExcelUtil<CompContestant>(CompContestant.class);
        util.exportExcel(response, list, "竞赛选手数据");
    }

    /**
     * 获取竞赛选手详细信息
     */
    @ApiOperation("获取竞赛选手详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(value = "竞赛选手ID", required = true)@PathVariable("id") Long id)
    {
        return success(compContestantService.selectCompContestantById(id));
    }

    /**
     * 新增竞赛选手
     */
    @ApiOperation("新增竞赛选手")
    @Log(title = "竞赛选手", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam(value = "竞赛选手参数", required = true)@RequestBody CompContestantDTO compContestant)
    {
        return toAjax(compContestantService.insertCompContestant(compContestant));
    }

    /**
     * 修改竞赛选手
     */
    @ApiOperation("修改竞赛选手")
    @Log(title = "竞赛选手", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam(value = "竞赛选手参数", required = true)@RequestBody List<CompContestant> compContestants)
    {
        return toAjax(compContestantService.updateCompContestants(compContestants));
    }

    /**
     * 删除竞赛选手
     */
    @ApiOperation("删除竞赛选手")
    @Log(title = "竞赛选手", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam(value = "竞赛选手ID数组", required = true)@PathVariable Long[] ids)
    {
        return toAjax(compContestantService.deleteCompContestantByIds(ids));
    }

    /**
     * 获取竞赛选手模板 ->从实体类直接获得
     * @param response
     */
    @ApiOperation("获取竞赛选手模板 ->从实体类直接获得")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<CompContestantDTO> util = new ExcelUtil<CompContestantDTO>(CompContestantDTO.class);
        util.importTemplateExcel(response, "竞赛选手模板");
    }

    /**
     * 竞赛选手-学生信息批量导入
     * @param file
     * @param competitionId
     * @return
     * @throws Exception
     */
    @ApiOperation("竞赛选手-学生信息批量导入")
    @PostMapping("/importData")
    public AjaxResult importData(@ApiParam(value = "文件", required = true) MultipartFile file,Long competitionId,Long competitionType) throws Exception
    {
        ExcelUtil<CompContestantDTO> util = new ExcelUtil<CompContestantDTO>(CompContestantDTO.class);
        List<CompContestantDTO> studentList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = compContestantService.importCompContestant(studentList, operName,competitionId,competitionType);
        return success(message);
    }
}
