package com.ruoyi.web.service.impl;

import java.util.List;
import java.math.BigDecimal;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.Project2scoreRuleDetailMapper;
import com.ruoyi.web.domain.Project2scoreRuleDetail;
import com.ruoyi.web.service.IProject2scoreRuleDetailService;
import com.ruoyi.web.domain.Project2scoreRuleCategory;
import com.ruoyi.web.mapper.Project2scoreRuleCategoryMapper;
import com.ruoyi.common.exception.ServiceException;

/**
 * 积分规则详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class Project2scoreRuleDetailServiceImpl implements IProject2scoreRuleDetailService 
{
    @Autowired
    private Project2scoreRuleDetailMapper project2scoreRuleDetailMapper;

    @Autowired
    private Project2scoreRuleCategoryMapper project2scoreRuleCategoryMapper;

    /**
     * 查询积分规则详情
     * 
     * @param id 积分规则详情主键
     * @return 积分规则详情
     */
    @Override
    public Project2scoreRuleDetail selectProject2scoreRuleDetailById(Long id)
    {
        return project2scoreRuleDetailMapper.selectProject2scoreRuleDetailById(id);
    }

    /**
     * 查询积分规则详情列表
     * 
     * @param project2scoreRuleDetail 积分规则详情
     * @return 积分规则详情
     */
    @Override
    public List<Project2scoreRuleDetail> selectProject2scoreRuleDetailList(Project2scoreRuleDetail project2scoreRuleDetail)
    {
        return project2scoreRuleDetailMapper.selectProject2scoreRuleDetailList(project2scoreRuleDetail);
    }

    /**
     * 新增积分规则详情
     * 
     * @param project2scoreRuleDetail 积分规则详情
     * @return 结果
     */
    @Override
    public int insertProject2scoreRuleDetail(Project2scoreRuleDetail project2scoreRuleDetail)
    {
        // 1. 验证积分值
        if (project2scoreRuleDetail.getScoreValue() != null 
            && project2scoreRuleDetail.getScoreValue().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("积分值不能为负数");
        }
        
        // 2. 验证显示顺序
        if (project2scoreRuleDetail.getRuleSort() != null 
            && project2scoreRuleDetail.getRuleSort() < 0) {
            throw new ServiceException("显示顺序不能为负数");
        }
        
        // 3. 验证规则名称是否重复
        Project2scoreRuleDetail query = new Project2scoreRuleDetail();
        query.setRuleName(project2scoreRuleDetail.getRuleName());
        List<Project2scoreRuleDetail> existingRules = project2scoreRuleDetailMapper.selectProject2scoreRuleDetailList(query);
        if (!existingRules.isEmpty()) {
            throw new ServiceException("规则名称已存在");
        }
        
        // 检查分类是否存在
        if (project2scoreRuleDetail.getCategoryId() != null) {
            Project2scoreRuleCategory category = project2scoreRuleCategoryMapper.selectProject2scoreRuleCategoryById(project2scoreRuleDetail.getCategoryId());
            if (category == null) {
                throw new ServiceException("所选分类不存在");
            }
            
            // 检查分类下是否已存在规则详情
            query = new Project2scoreRuleDetail();
            query.setCategoryId(project2scoreRuleDetail.getCategoryId());
            existingRules = project2scoreRuleDetailMapper.selectProject2scoreRuleDetailList(query);
            if (!existingRules.isEmpty()) {
                throw new ServiceException("该分类下已存在规则详情,不允许重复添加");
            }
        }
        
        project2scoreRuleDetail.setCreateTime(DateUtils.getNowDate());
        return project2scoreRuleDetailMapper.insertProject2scoreRuleDetail(project2scoreRuleDetail);
    }

    /**
     * 修改积分规则详情
     * 
     * @param project2scoreRuleDetail 积分规则详情
     * @return 结果
     */
    @Override
    public int updateProject2scoreRuleDetail(Project2scoreRuleDetail project2scoreRuleDetail)
    {
        // 1. 验证积分值
        if (project2scoreRuleDetail.getScoreValue() != null 
            && project2scoreRuleDetail.getScoreValue().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("积分值不能为负数");
        }
        
        // 2. 验证显示顺序
        if (project2scoreRuleDetail.getRuleSort() != null 
            && project2scoreRuleDetail.getRuleSort() < 0) {
            throw new ServiceException("显示顺序不能为负数");
        }
        
        // 3. 验证规则名称是否重复(排除自身)
        Project2scoreRuleDetail query = new Project2scoreRuleDetail();
        query.setRuleName(project2scoreRuleDetail.getRuleName());
        List<Project2scoreRuleDetail> existingRules = project2scoreRuleDetailMapper.selectProject2scoreRuleDetailList(query);
        for (Project2scoreRuleDetail rule : existingRules) {
            if (!rule.getId().equals(project2scoreRuleDetail.getId())) {
                throw new ServiceException("规则名称已存在");
            }
        }
        
        // 4. 检查分类是否存在
        if (project2scoreRuleDetail.getCategoryId() != null) {
            Project2scoreRuleCategory category = project2scoreRuleCategoryMapper.selectProject2scoreRuleCategoryById(project2scoreRuleDetail.getCategoryId());
            if (category == null) {
                throw new ServiceException("所选分类不存在");
            }
            
            // 5. 如果要启用规则详情，检查分类状态
            if (project2scoreRuleDetail.getStatus() != null) {
                if ("0".equals(project2scoreRuleDetail.getStatus())) {  // 要启用规则详情
                    if (!"0".equals(category.getStatus())) {
                        throw new ServiceException("无法启用规则详情：所属分类已禁用");
                    }
                    
                    // 检查父级分类状态
                    if (category.getParentId() != null && category.getParentId() != 0L) {
                        String[] ancestors = category.getAncestors().split(",");
                        for (String ancestorId : ancestors) {
                            if (!"0".equals(ancestorId)) {  // 跳过根节点
                                Project2scoreRuleCategory ancestor = project2scoreRuleCategoryMapper.selectProject2scoreRuleCategoryById(Long.valueOf(ancestorId));
                                if (ancestor != null && !"0".equals(ancestor.getStatus())) {
                                    throw new ServiceException("无法启用规则详情：上级分类" + ancestor.getCategoryName() + "已禁用");
                                }
                            }
                        }
                    }
                } else {  // 要禁用规则详情
                    // 只同步禁用直接所属的分类
                    category.setStatus("1"); // 1表示禁用
                    category.setUpdateTime(DateUtils.getNowDate());
                    project2scoreRuleCategoryMapper.updateProject2scoreRuleCategory(category);
                }
            }
            
            // 6. 检查分类下是否已存在其他规则详情(排除当前记录)
            query = new Project2scoreRuleDetail();
            query.setCategoryId(project2scoreRuleDetail.getCategoryId());
            existingRules = project2scoreRuleDetailMapper.selectProject2scoreRuleDetailList(query);
            for (Project2scoreRuleDetail rule : existingRules) {
                if (!rule.getId().equals(project2scoreRuleDetail.getId())) {
                    throw new ServiceException("该分类下已存在其他规则详情,不允许重复添加");
                }
            }
        }
        
        project2scoreRuleDetail.setUpdateTime(DateUtils.getNowDate());
        return project2scoreRuleDetailMapper.updateProject2scoreRuleDetail(project2scoreRuleDetail);
    }

    /**
     * 批量删除积分规则详情
     * 
     * @param ids 需要删除的积分规则详情主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreRuleDetailByIds(Long[] ids)
    {
        return project2scoreRuleDetailMapper.deleteProject2scoreRuleDetailByIds(ids);
    }

    /**
     * 删除积分规则详情信息
     * 
     * @param id 积分规则详情主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreRuleDetailById(Long id)
    {
        return project2scoreRuleDetailMapper.deleteProject2scoreRuleDetailById(id);
    }
}
