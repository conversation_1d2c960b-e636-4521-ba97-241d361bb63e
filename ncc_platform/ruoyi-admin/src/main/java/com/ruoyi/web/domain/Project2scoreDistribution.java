package com.ruoyi.web.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 积分分配对象 project2score_distribution
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class Project2scoreDistribution extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分配ID */
    private Long id;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 团队成员ID */
    @Excel(name = "团队成员ID")
    private Long userId;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 分配比例（%） */
    @Excel(name = "分配比例", readConverterExp = "%=")
    private BigDecimal ratio;

    /** 分配积分 */
    @Excel(name = "分配积分")
    private BigDecimal score;

    /** 是否为负责人（0否 1是） */
    @Excel(name = "是否为负责人", readConverterExp = "0=否,1=是")
    private String isLeader;

    /** 成员类型（0院内用户 1外部人员） */
    @Excel(name = "成员类型", readConverterExp = "0=院内用户,1=外部人员")
    private String memberType;

    /** 成员姓名 */
    @Excel(name = "成员姓名")
    private String memberName;

    /** 外部人员身份说明 */
    @Excel(name = "外部人员身份说明")
    private String memberIdentity;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setRatio(BigDecimal ratio) 
    {
        this.ratio = ratio;
    }

    public BigDecimal getRatio() 
    {
        return ratio;
    }
    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }
    public void setIsLeader(String isLeader) 
    {
        this.isLeader = isLeader;
    }

    public String getIsLeader() 
    {
        return isLeader;
    }

    public void setMemberType(String memberType) 
    {
        this.memberType = memberType;
    }

    public String getMemberType() 
    {
        return memberType;
    }

    public void setMemberName(String memberName) 
    {
        this.memberName = memberName;
    }

    public String getMemberName() 
    {
        return memberName;
    }

    public void setMemberIdentity(String memberIdentity) 
    {
        this.memberIdentity = memberIdentity;
    }

    public String getMemberIdentity() 
    {
        return memberIdentity;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("userId", getUserId())
            .append("deptId", getDeptId())
            .append("ratio", getRatio())
            .append("score", getScore())
            .append("isLeader", getIsLeader())
            .append("memberType", getMemberType())
            .append("memberName", getMemberName())
            .append("memberIdentity", getMemberIdentity())
            .append("deptName", getDeptName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
