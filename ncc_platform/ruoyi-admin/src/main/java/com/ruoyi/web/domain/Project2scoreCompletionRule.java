package com.ruoyi.web.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 科研指标规则对象 project2score_completion_rule
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
public class Project2scoreCompletionRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private Long id;

    /** 职务 */
    @Excel(name = "职务")
    private String job;

    /** 职称 */
    @Excel(name = "职称")
    private String ranks;

    /** 科研积分目标 */
    @Excel(name = "科研积分目标")
    private BigDecimal target;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setJob(String job) 
    {
        this.job = job;
    }

    public String getJob() 
    {
        return job;
    }
    public void setRanks(String ranks) 
    {
        this.ranks = ranks;
    }

    public String getRanks() 
    {
        return ranks;
    }
    public void setTarget(BigDecimal target) 
    {
        this.target = target;
    }

    public BigDecimal getTarget() 
    {
        return target;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("job", getJob())
            .append("ranks", getRanks())
            .append("target", getTarget())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
