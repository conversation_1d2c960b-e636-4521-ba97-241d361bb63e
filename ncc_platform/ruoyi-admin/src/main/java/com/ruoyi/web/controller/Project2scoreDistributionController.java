package com.ruoyi.web.controller;

import java.util.List;

import com.ruoyi.web.domain.dto.BatchSaveDistributionsDTO;
import com.ruoyi.web.domain.vo.Project2scoreDistributionExportVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.web.domain.Project2scoreDistribution;
import com.ruoyi.web.service.IProject2scoreDistributionService;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.web.domain.vo.Project2scoreDistributionVO;

import javax.servlet.http.HttpServletResponse;

/**
 * 积分分配Controller
 * 
 * <AUTHOR>
 * @date 2023-02-03
 */
@RestController
@RequestMapping("/web/distribution")
public class Project2scoreDistributionController extends BaseController
{
    @Autowired
    private IProject2scoreDistributionService project2scoreDistributionService;

    /**
     * 查询当前用户参与的团队项目积分分配列表
     */
    @PreAuthorize("@ss.hasPermi('web:distribution:list')")
    @GetMapping("/mylist")
    public TableDataInfo myList(Project2scoreDistributionVO project2scoreDistribution)
    {
        startPage();
        // 获取当前登录用户ID
        LoginUser loginUser = SecurityUtils.getLoginUser();
        project2scoreDistribution.setUserId(loginUser.getUserId());
        List<Project2scoreDistributionVO> list = project2scoreDistributionService.selectMyDistributionList(project2scoreDistribution);
        return getDataTable(list);
    }

    /**
     * 根据项目ID查询分配记录
     */
    @PreAuthorize("@ss.hasPermi('web:distribution:list')")
    @GetMapping("/project/{projectId}")
    public AjaxResult getProjectDistributions(@PathVariable("projectId") Long projectId)
    {
        Project2scoreDistribution query = new Project2scoreDistribution();
        query.setProjectId(projectId);
        return success(project2scoreDistributionService.selectProject2scoreDistributionList(query));
    }

    /**
     * 获取积分分配详细信息
     */
    @PreAuthorize("@ss.hasPermi('web:distribution:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(project2scoreDistributionService.selectProject2scoreDistributionById(id));
    }

    /**
     * 导出积分分配列表
     */
    @PreAuthorize("@ss.hasPermi('web:distribution:export')")
    @Log(title = "积分分配", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Project2scoreDistributionVO project2scoreDistribution)
    {
        // 获取当前登录用户ID
        LoginUser loginUser = SecurityUtils.getLoginUser();
        project2scoreDistribution.setUserId(loginUser.getUserId());
        List<Project2scoreDistributionExportVO> list = project2scoreDistributionService.selectDistributionExportList(project2scoreDistribution);
        ExcelUtil<Project2scoreDistributionExportVO> util = new ExcelUtil<Project2scoreDistributionExportVO>(Project2scoreDistributionExportVO.class);
        util.exportExcel(response, list, "团队积分情况");
    }

    /**
     * 新增积分分配
     */
    @PreAuthorize("@ss.hasPermi('web:distribution:add')")
    @Log(title = "积分分配", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Project2scoreDistribution project2scoreDistribution)
    {
        return toAjax(project2scoreDistributionService.insertProject2scoreDistribution(project2scoreDistribution));
    }

    /**
     * 修改积分分配
     */
    @PreAuthorize("@ss.hasPermi('web:distribution:edit')")
    @Log(title = "积分分配", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Project2scoreDistribution project2scoreDistribution)
    {
        return toAjax(project2scoreDistributionService.updateProject2scoreDistribution(project2scoreDistribution));
    }

    /**
     * 删除积分分配
     */
    @PreAuthorize("@ss.hasPermi('web:distribution:remove')")
    @Log(title = "积分分配", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(project2scoreDistributionService.deleteProject2scoreDistributionByIds(ids));
    }

    /**
     * 批量保存团队成员分配
     */
    @PostMapping("/batchSave")
    public AjaxResult batchSave(@RequestBody BatchSaveDistributionsDTO dto)
    {
        // 设置projectId和处理外部用户
        if (dto.getDistributions() != null) {
            dto.getDistributions().forEach(item -> {
                item.setProjectId(dto.getProjectId());
                // 处理外部用户
                if ("1".equals(item.getMemberType())) {
                    item.setUserId(0L);
                    item.setDeptId(null);
                }
            });
        }
        return toAjax(project2scoreDistributionService.batchSaveDistributions(dto.getDistributions()));
    }
} 