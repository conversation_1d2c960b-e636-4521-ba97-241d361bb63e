package com.ruoyi.web.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.Project2scoreRecordVO;
import com.ruoyi.web.domain.vo.Project2scoreRecordExportVO;
import com.ruoyi.web.service.IProject2scoreRecordExtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.web.service.IProject2scoreRuleDetailService;
import com.ruoyi.web.service.IProject2scoreRuleCategoryService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 积分记录Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/scoreRecordExt")
public class Project2scoreRecordExtController extends BaseController {
    @Autowired
    private IProject2scoreRecordExtService project2scoreRecordExtService;

    @Autowired
    private ISysDeptService deptService;
    
    @Autowired
    private IProject2scoreRuleDetailService ruleDetailService;
    
    @Autowired
    private IProject2scoreRuleCategoryService ruleCategoryService;

    /**
     * 查询积分记录列表
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(Project2scoreRecord project2scoreRecord) {
        startPage();
        
        // 如果不是管理员或领导，且没有特别指定要查询某个用户，则只能查询自己的记录
        project2scoreRecord.setUserId(SecurityUtils.getUserId());
        List<Project2scoreRecordVO> list = project2scoreRecordExtService.selectProject2scoreRecordVOList(project2scoreRecord);
        return getDataTable(list);
    }

    /**
     * 导出积分记录列表
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRecord:export')")
    @Log(title = "积分记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Project2scoreRecord project2scoreRecord)
    {
        // 设置当前用户ID
        project2scoreRecord.setUserId(SecurityUtils.getUserId());
        
        // 如果没有选择时间范围，则按项目时间升序排序
        if (project2scoreRecord.getParams() == null) {
            project2scoreRecord.setParams(new HashMap<String, Object>());
        }
        if (project2scoreRecord.getParams().get("beginProjectTime") == null && 
            project2scoreRecord.getParams().get("endProjectTime") == null) {
            project2scoreRecord.getParams().put("orderBy", "r.project_time ASC");
        }
        
        List<Project2scoreRecordExportVO> list = project2scoreRecordExtService.selectExportList(project2scoreRecord);
        
        // 构建标题
        String beginTime = project2scoreRecord.getParams().get("beginProjectTime") != null 
            ? project2scoreRecord.getParams().get("beginProjectTime").toString() 
            : "";
        String endTime = project2scoreRecord.getParams().get("endProjectTime") != null 
            ? project2scoreRecord.getParams().get("endProjectTime").toString() 
            : "";
            
        // 构建完整标题
        StringBuilder titleBuilder = new StringBuilder();
        
        // 添加用户昵称
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        if (StringUtils.isNotEmpty(nickName)) {
            titleBuilder.append("[").append(nickName).append("]");
        }
        
        // 添加时间范围
        titleBuilder.append(StringUtils.isNotEmpty(beginTime) ? beginTime : "开始");
        titleBuilder.append("至");
        titleBuilder.append(StringUtils.isNotEmpty(endTime) ? endTime : "至今");
        
        // 根据规则分类ID查询分类名称
        if (project2scoreRecord.getCategoryId() != null) {
            String categoryName = ruleCategoryService.selectProject2scoreRuleCategoryById(project2scoreRecord.getCategoryId()).getCategoryName();
            if (StringUtils.isNotEmpty(categoryName)) {
                titleBuilder.append(" [").append(categoryName);
                // 如果同时选择了具体规则，则显示规则名称
                if (project2scoreRecord.getRuleId() != null) {
                    String ruleName = ruleDetailService.selectProject2scoreRuleDetailById(project2scoreRecord.getRuleId()).getRuleName();
                    if (StringUtils.isNotEmpty(ruleName)) {
                        titleBuilder.append("-").append(ruleName);
                    }
                }
                titleBuilder.append("]");
            }
        }
        // 如果只选择了规则没有选择分类
        else if (project2scoreRecord.getRuleId() != null) {
            String ruleName = ruleDetailService.selectProject2scoreRuleDetailById(project2scoreRecord.getRuleId()).getRuleName();
            if (StringUtils.isNotEmpty(ruleName)) {
                titleBuilder.append(" [").append(ruleName).append("]");
            }
        }
        
        titleBuilder.append("积分记录统计结果");
            
        ExcelUtil<Project2scoreRecordExportVO> util = new ExcelUtil<>(Project2scoreRecordExportVO.class);
        util.exportExcel(response, list, "积分记录", titleBuilder.toString());
    }

    /**
     * 获取积分记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(project2scoreRecordExtService.selectProject2scoreRecordVOById(id));
    }
} 