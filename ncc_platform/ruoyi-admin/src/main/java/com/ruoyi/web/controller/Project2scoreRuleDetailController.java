package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.Project2scoreRuleDetail;
import com.ruoyi.web.service.IProject2scoreRuleDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 积分规则详情Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/web/scoreRuleDetail")
public class Project2scoreRuleDetailController extends BaseController
{
    @Autowired
    private IProject2scoreRuleDetailService project2scoreRuleDetailService;

    /**
     * 查询积分规则详情列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Project2scoreRuleDetail project2scoreRuleDetail)
    {
        startPage();
        List<Project2scoreRuleDetail> list = project2scoreRuleDetailService.selectProject2scoreRuleDetailList(project2scoreRuleDetail);
        return getDataTable(list);
    }

    /**
     * 导出积分规则详情列表
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRuleDetail:export')")
    @Log(title = "积分规则详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Project2scoreRuleDetail project2scoreRuleDetail)
    {
        List<Project2scoreRuleDetail> list = project2scoreRuleDetailService.selectProject2scoreRuleDetailList(project2scoreRuleDetail);
        ExcelUtil<Project2scoreRuleDetail> util = new ExcelUtil<Project2scoreRuleDetail>(Project2scoreRuleDetail.class);
        util.exportExcel(response, list, "积分规则详情数据");
    }

    /**
     * 获取积分规则详情详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(project2scoreRuleDetailService.selectProject2scoreRuleDetailById(id));
    }

    /**
     * 新增积分规则详情
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRuleDetail:add')")
    @Log(title = "积分规则详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Project2scoreRuleDetail project2scoreRuleDetail)
    {
        return toAjax(project2scoreRuleDetailService.insertProject2scoreRuleDetail(project2scoreRuleDetail));
    }

    /**
     * 修改积分规则详情
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRuleDetail:edit')")
    @Log(title = "积分规则详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Project2scoreRuleDetail project2scoreRuleDetail)
    {
        return toAjax(project2scoreRuleDetailService.updateProject2scoreRuleDetail(project2scoreRuleDetail));
    }

    /**
     * 删除积分规则详情
     */
    @PreAuthorize("@ss.hasPermi('web:scoreRuleDetail:remove')")
    @Log(title = "积分规则详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(project2scoreRuleDetailService.deleteProject2scoreRuleDetailByIds(ids));
    }
}
