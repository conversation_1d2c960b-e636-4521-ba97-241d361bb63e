package com.ruoyi.web.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.Project2scoreCompletionRuleMapper;
import com.ruoyi.web.domain.Project2scoreCompletionRule;
import com.ruoyi.web.service.IProject2scoreCompletionRuleService;

/**
 * 科研指标规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class Project2scoreCompletionRuleServiceImpl implements IProject2scoreCompletionRuleService
{
    @Autowired
    private Project2scoreCompletionRuleMapper project2scoreCompletionRuleMapper;

    /**
     * 查询科研指标规则
     *
     * @param id 科研指标规则主键
     * @return 科研指标规则
     */
    @Override
    public Project2scoreCompletionRule selectProject2scoreCompletionRuleById(Long id)
    {
        return project2scoreCompletionRuleMapper.selectProject2scoreCompletionRuleById(id);
    }

    /**
     * 查询科研指标规则列表
     *
     * @param project2scoreCompletionRule 科研指标规则
     * @return 科研指标规则
     */
    @Override
    public List<Project2scoreCompletionRule> selectProject2scoreCompletionRuleList(Project2scoreCompletionRule project2scoreCompletionRule)
    {
        return project2scoreCompletionRuleMapper.selectProject2scoreCompletionRuleList(project2scoreCompletionRule);
    }

    /**
     * 新增科研指标规则
     *
     * @param project2scoreCompletionRule 科研指标规则
     * @return 结果
     */
    @Override
    public int insertProject2scoreCompletionRule(Project2scoreCompletionRule project2scoreCompletionRule)
    {
        // 验证job和ranks至少有一个不为空
        validateJobAndRanks(project2scoreCompletionRule);

        project2scoreCompletionRule.setCreateTime(DateUtils.getNowDate());
        return project2scoreCompletionRuleMapper.insertProject2scoreCompletionRule(project2scoreCompletionRule);
    }

    /**
     * 修改科研指标规则
     *
     * @param project2scoreCompletionRule 科研指标规则
     * @return 结果
     */
    @Override
    public int updateProject2scoreCompletionRule(Project2scoreCompletionRule project2scoreCompletionRule)
    {
        // 验证job和ranks至少有一个不为空
        validateJobAndRanks(project2scoreCompletionRule);

        project2scoreCompletionRule.setUpdateTime(DateUtils.getNowDate());
        return project2scoreCompletionRuleMapper.updateProject2scoreCompletionRule(project2scoreCompletionRule);
    }

    /**
     * 批量删除科研指标规则
     *
     * @param ids 需要删除的科研指标规则主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreCompletionRuleByIds(Long[] ids)
    {
        return project2scoreCompletionRuleMapper.deleteProject2scoreCompletionRuleByIds(ids);
    }

    /**
     * 删除科研指标规则信息
     *
     * @param id 科研指标规则主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreCompletionRuleById(Long id)
    {
        return project2scoreCompletionRuleMapper.deleteProject2scoreCompletionRuleById(id);
    }

    /**
     * 验证job和ranks字段，确保至少有一个不为空
     *
     * @param rule 科研指标规则
     */
    private void validateJobAndRanks(Project2scoreCompletionRule rule) {
        String job = rule.getJob();
        String ranks = rule.getRanks();

        if ((job == null || job.trim().isEmpty()) && (ranks == null || ranks.trim().isEmpty())) {
            throw new RuntimeException("职务和职称至少填写一个");
        }
    }
}
