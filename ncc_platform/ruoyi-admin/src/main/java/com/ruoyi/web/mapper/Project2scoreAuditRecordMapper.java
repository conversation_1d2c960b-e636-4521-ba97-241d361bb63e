package com.ruoyi.web.mapper;

import java.util.List;
import com.ruoyi.web.domain.Project2scoreAuditRecord;

/**
 * 审核记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface Project2scoreAuditRecordMapper 
{
    /**
     * 查询审核记录
     * 
     * @param id 审核记录主键
     * @return 审核记录
     */
    public Project2scoreAuditRecord selectProject2scoreAuditRecordById(Long id);

    /**
     * 查询审核记录列表
     * 
     * @param project2scoreAuditRecord 审核记录
     * @return 审核记录集合
     */
    public List<Project2scoreAuditRecord> selectProject2scoreAuditRecordList(Project2scoreAuditRecord project2scoreAuditRecord);

    /**
     * 新增审核记录
     * 
     * @param project2scoreAuditRecord 审核记录
     * @return 结果
     */
    public int insertProject2scoreAuditRecord(Project2scoreAuditRecord project2scoreAuditRecord);

    /**
     * 修改审核记录
     * 
     * @param project2scoreAuditRecord 审核记录
     * @return 结果
     */
    public int updateProject2scoreAuditRecord(Project2scoreAuditRecord project2scoreAuditRecord);

    /**
     * 删除审核记录
     * 
     * @param id 审核记录主键
     * @return 结果
     */
    public int deleteProject2scoreAuditRecordById(Long id);

    /**
     * 批量删除审核记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProject2scoreAuditRecordByIds(Long[] ids);

    /**
     * 获取最近一次审核记录
     * 
     * @param projectId 项目ID
     * @return 审核记录
     */
    public Project2scoreAuditRecord selectLatestAuditRecord(Long projectId);
}
