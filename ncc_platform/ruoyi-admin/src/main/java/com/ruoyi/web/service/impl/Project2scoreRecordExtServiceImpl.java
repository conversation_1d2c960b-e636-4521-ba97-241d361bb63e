package com.ruoyi.web.service.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.Project2scoreRecordVO;
import com.ruoyi.web.domain.vo.Project2scoreRecordExportVO;
import com.ruoyi.web.mapper.Project2scoreRecordMapper;
import com.ruoyi.web.service.IProject2scoreRecordExtService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 积分记录Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class Project2scoreRecordExtServiceImpl implements IProject2scoreRecordExtService {
    @Autowired
    private Project2scoreRecordMapper project2scoreRecordMapper;

    /**
     * 查询积分记录列表
     * 
     * @param record 积分记录
     * @return 积分记录
     */
    @Override
    public List<Project2scoreRecordVO> selectProject2scoreRecordVOList(Project2scoreRecord record) {
        return project2scoreRecordMapper.selectProject2scoreRecordVOList(record);
    }

    /**
     * 查询积分记录详细信息
     * 
     * @param id 积分记录主键
     * @return 积分记录
     */
    @Override
    public Project2scoreRecordVO selectProject2scoreRecordVOById(Long id) {
        return project2scoreRecordMapper.selectProject2scoreRecordVOById(id);
    }
    
    /**
     * 导出积分记录列表
     */
    @Override
    public List<Project2scoreRecordExportVO> selectExportList(Project2scoreRecord record) {
        return project2scoreRecordMapper.selectExportList(record);
    }
} 