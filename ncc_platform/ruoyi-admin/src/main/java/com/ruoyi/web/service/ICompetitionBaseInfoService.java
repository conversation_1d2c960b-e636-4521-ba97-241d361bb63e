package com.ruoyi.web.service;

import java.util.List;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.web.domain.CompetitionBaseInfo;
import org.springframework.stereotype.Service;

/**
 * 竞赛信息基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public interface ICompetitionBaseInfoService 
{
    /**
     * 查询竞赛信息基础信息
     * 
     * @param id 竞赛信息基础信息主键
     * @return 竞赛信息基础信息
     */
    public CompetitionBaseInfo selectCompetitionBaseInfoById(Long id);

    /**
     * 查询竞赛信息基础信息列表
     * 
     * @param competitionBaseInfo 竞赛信息基础信息
     * @return 竞赛信息基础信息集合
     */
    public List<CompetitionBaseInfo> selectCompetitionBaseInfoList(CompetitionBaseInfo competitionBaseInfo, LoginUser user);

    /**
     * 新增竞赛信息基础信息
     * 
     * @param competitionBaseInfo 竞赛信息基础信息
     * @return 结果
     */
    public int insertCompetitionBaseInfo(CompetitionBaseInfo competitionBaseInfo);

    /**
     * 修改竞赛信息基础信息
     * 
     * @param competitionBaseInfo 竞赛信息基础信息
     * @return 结果
     */
    public int updateCompetitionBaseInfo(CompetitionBaseInfo competitionBaseInfo);

    /**
     * 批量删除竞赛信息基础信息
     * 
     * @param ids 需要删除的竞赛信息基础信息主键集合
     * @return 结果
     */
    public int deleteCompetitionBaseInfoByIds(Long[] ids);

    /**
     * 删除竞赛信息基础信息信息
     * 
     * @param id 竞赛信息基础信息主键
     * @return 结果
     */
    public int deleteCompetitionBaseInfoById(Long id);
}
