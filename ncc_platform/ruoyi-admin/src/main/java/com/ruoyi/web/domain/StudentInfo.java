package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 学生信息对象 student_info
 * 
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel(value = "StudentInfo", description = "学生信息对象")
@TableName("`student_info`")
@Data
public class StudentInfo
{
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /** 学生主键 */
    @ApiModelProperty(value = "学生主键", required = true)
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 学生姓名 */
    @ApiModelProperty(value = "学生姓名", required = true)
    @Excel(name = "学生姓名")
    private String name;

    /** 学生专业 */
    @ApiModelProperty(value = "学生专业", required = true)
    @Excel(name = "学生专业")
    private String major;

    /** 学生班级 */
    @ApiModelProperty(value = "学生班级", required = true)
    @Excel(name = "学生班级")
    private String className;

    /** 学生学号 */
    @ApiModelProperty(value = "学生学号", required = true)
    @Excel(name = "学生学号")
    private String no;

    /** 性别(0:未定义,1:男,2:女) */
    @ApiModelProperty(value = "性别(0:未定义,1:男,2:女)", required = true)
    @Excel(name = "性别", readConverterExp = "0=未定义,1=男,2=女")
    private Integer gender;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
