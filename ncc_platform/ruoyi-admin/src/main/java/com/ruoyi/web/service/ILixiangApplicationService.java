package com.ruoyi.web.service;

import java.util.List;
import java.util.Map;

import com.deepoove.poi.XWPFTemplate;
import com.ruoyi.web.domain.LixiangApplication;

/**
 * 立项信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface ILixiangApplicationService 
{
    /**
     * 查询立项信息
     * 
     * @param id 立项信息主键
     * @return 立项信息
     */
    public LixiangApplication selectLixiangApplicationById(Long id);

    /**
     * 查询立项信息列表
     * 
     * @param lixiangApplication 立项信息
     * @return 立项信息集合
     */
    public List<LixiangApplication> selectLixiangApplicationList(LixiangApplication lixiangApplication);

    /**
     * 新增立项信息
     * 
     * @param lixiangApplication 立项信息
     * @return 结果
     */
    public int insertLixiangApplication(LixiangApplication lixiangApplication);

    /**
     * 修改立项信息
     * 
     * @param lixiangApplication 立项信息
     * @return 结果
     */
    public int updateLixiangApplication(LixiangApplication lixiangApplication);

    /**
     * 批量删除立项信息
     * 
     * @param ids 需要删除的立项信息主键集合
     * @return 结果
     */
    public int deleteLixiangApplicationByIds(Long[] ids);

    /**
     * 删除立项信息信息
     * 
     * @param id 立项信息主键
     * @return 结果
     */
    public int deleteLixiangApplicationById(Long id);

    /**
     * 获取模板
     * @param lixiangApplication
     * @return
     */
    public Map<String, Object> getDataList(LixiangApplication lixiangApplication);
}
