package com.ruoyi.web.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.Project2scoreRecordMapper;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.Project2scoreRecordExportVO;
import com.ruoyi.web.service.IProject2scoreRecordService;

/**
 * 积分记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class Project2scoreRecordServiceImpl implements IProject2scoreRecordService 
{
    @Autowired
    private Project2scoreRecordMapper project2scoreRecordMapper;

    /**
     * 查询积分记录
     * 
     * @param id 积分记录主键
     * @return 积分记录
     */
    @Override
    public Project2scoreRecord selectProject2scoreRecordById(Long id)
    {
        return project2scoreRecordMapper.selectProject2scoreRecordById(id);
    }

    /**
     * 查询积分记录列表
     * 
     * @param project2scoreRecord 积分记录
     * @return 积分记录
     */
    @Override
    public List<Project2scoreRecord> selectProject2scoreRecordList(Project2scoreRecord project2scoreRecord)
    {
        return project2scoreRecordMapper.selectProject2scoreRecordList(project2scoreRecord);
    }

    /**
     * 查询积分记录导出列表
     * 
     * @param project2scoreRecord 查询条件
     * @return 积分记录导出数据集合
     */
    @Override
    public List<Project2scoreRecordExportVO> selectExportList(Project2scoreRecord project2scoreRecord) {
        return project2scoreRecordMapper.selectExportList(project2scoreRecord);
    }

    /**
     * 新增积分记录
     * 
     * @param project2scoreRecord 积分记录
     * @return 结果
     */
    @Override
    public int insertProject2scoreRecord(Project2scoreRecord project2scoreRecord)
    {
        project2scoreRecord.setCreateTime(DateUtils.getNowDate());
        return project2scoreRecordMapper.insertProject2scoreRecord(project2scoreRecord);
    }

    /**
     * 修改积分记录
     * 
     * @param project2scoreRecord 积分记录
     * @return 结果
     */
    @Override
    public int updateProject2scoreRecord(Project2scoreRecord project2scoreRecord)
    {
        return project2scoreRecordMapper.updateProject2scoreRecord(project2scoreRecord);
    }

    /**
     * 批量删除积分记录
     * 
     * @param ids 需要删除的积分记录主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreRecordByIds(Long[] ids)
    {
        return project2scoreRecordMapper.deleteProject2scoreRecordByIds(ids);
    }

    /**
     * 删除积分记录信息
     * 
     * @param id 积分记录主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreRecordById(Long id)
    {
        return project2scoreRecordMapper.deleteProject2scoreRecordById(id);
    }

    /**
     * 根据项目ID删除积分记录
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    @Override
    public int deleteProject2scoreRecordByProjectId(Long projectId)
    {
        return project2scoreRecordMapper.deleteProject2scoreRecordByProjectId(projectId);
    }
}
