package com.ruoyi.web.service;

import java.util.List;
import com.ruoyi.web.domain.Project2scoreRuleCategory;

/**
 * 积分规则分类Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IProject2scoreRuleCategoryService 
{
    /**
     * 查询积分规则分类
     * 
     * @param id 积分规则分类主键
     * @return 积分规则分类
     */
    public Project2scoreRuleCategory selectProject2scoreRuleCategoryById(Long id);

    /**
     * 查询积分规则分类列表
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 积分规则分类集合
     */
    public List<Project2scoreRuleCategory> selectProject2scoreRuleCategoryList(Project2scoreRuleCategory project2scoreRuleCategory);

    /**
     * 新增积分规则分类
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 结果
     */
    public int insertProject2scoreRuleCategory(Project2scoreRuleCategory project2scoreRuleCategory);

    /**
     * 修改积分规则分类
     * 
     * @param project2scoreRuleCategory 积分规则分类
     * @return 结果
     */
    public int updateProject2scoreRuleCategory(Project2scoreRuleCategory project2scoreRuleCategory);

    /**
     * 批量删除积分规则分类
     * 
     * @param ids 需要删除的积分规则分类主键集合
     * @return 结果
     */
    public int deleteProject2scoreRuleCategoryByIds(Long[] ids);

    /**
     * 删除积分规则分类信息
     * 
     * @param id 积分规则分类主键
     * @return 结果
     */
    public int deleteProject2scoreRuleCategoryById(Long id);

    /**
     * 查找所有子节点
     *
     * @param id 积分规则分类主键
     * @return 结果
     */
    public List<Project2scoreRuleCategory> selectKidsProject2scoreRuleCategoryById(Long id);

    /**
     * 构建树
     */
    public List<Project2scoreRuleCategory> buildTree(List<Project2scoreRuleCategory> categories);

}
