package com.ruoyi.web.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.web.service.DAService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(value = "/web/DA", description = "数据分析")
@RestController
@RequestMapping("/web/DA")
public class DAController {
    @Autowired
    private DAService daService;

    /**
     * 基础信息获取
     * @return
     */
    @ApiOperation("基础信息获取")
    @GetMapping("/base")
    public AjaxResult base()
    {
        Map<String, Object> countNum = daService.getCountNum();
        return AjaxResult.success(countNum);
    }

    /**
     * 获取所有数据信息
     * @return
     */
    @ApiOperation("获取所有数据信息")
    @GetMapping("/allInfo")
    public AjaxResult dataAnalysis(@ApiParam(value = "获取所有信息参数", required = true) String major)
    {
        Map<String, Object> dataAnalysis = daService.getDataAnalysis(major);
        return AjaxResult.success(dataAnalysis);
    }
}
