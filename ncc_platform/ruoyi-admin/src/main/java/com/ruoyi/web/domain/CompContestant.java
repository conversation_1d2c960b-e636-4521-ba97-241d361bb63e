package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 竞赛选手对象 comp_contestant
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@TableName(value = "comp_contestant")
@Data
@ApiModel(value = "CompContestant", description = "竞赛选手对象")
public class CompContestant
{
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Excel(name = "主键")
    @ApiModelProperty(value = "主键", example = "主键",required = true)
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 比赛ID */
    @ApiModelProperty(value = "比赛ID", example = "比赛ID",required = true)
    @Excel(name = "比赛ID")
    private Long competitionId;

    /** 选手ID */
    @ApiModelProperty(value = "选手ID", example = "选手ID",required = true)
    @Excel(name = "选手ID")
    private String contestantId;

    @Excel(name = "职责及分工")
    @ApiModelProperty(value = "职责及分工", example = "职责及分工",required = true)
    private String contestantRole;

    /** 所获奖项 */
    @ApiModelProperty(value = "所获奖项", example = "所获奖项",required = true)
    @Excel(name = "所获奖项",dictType="awards")
    private String awards;

    /** 选手类型(0:学生,1:老师) */
    @ApiModelProperty(value = "选手类型(0:学生,1:老师)", example = "选手类型(0:学生,1:老师)",required = true)
    @Excel(name = "选手类型",dictType= "contestant_type")
    private Integer type;

    @Excel(name = "专业")
    @ApiModelProperty(value = "专业", example = "专业",required = true)
    private String major;

    @Excel(name = "学号/工号")
    @ApiModelProperty(value = "学号/工号", example = "学号/工号",required = true)
    private String no;
    @Excel(name = "队伍")
    @ApiModelProperty(value = "队伍", example = "队伍",required = true)
    private String group;

}
