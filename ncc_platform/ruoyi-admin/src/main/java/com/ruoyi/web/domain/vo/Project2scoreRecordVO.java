package com.ruoyi.web.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分记录视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Project2scoreRecordVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    @Excel(name = "记录ID")
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 部门ID */
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 项目ID */
    private Long projectId;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 规则ID */
    private Long ruleId;

    /** 分类ID */
    private Long categoryId;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String ruleName;

    /** 积分值 */
    @Excel(name = "积分值")
    private BigDecimal score;

    /** 积分动作 （0增加 1减少） */
    @Excel(name = "积分动作", readConverterExp = "0=增加,1=减少")
    private String type;

    /** 备注说明 */
    @Excel(name = "备注说明")
    private String remark;

    /** 项目时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date projectTime;
} 