package com.ruoyi.web.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 积分记录对象 project2score_record
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class Project2scoreRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 规则ID */
    @Excel(name = "规则ID")
    private Long ruleId;

    /** 分类ID */
    private Long categoryId;

    /** 积分值 */
    @Excel(name = "个人积分值", sort = 8)
    private BigDecimal score;

    /** 积分动作 （0增加 1减少） */
    @Excel(name = "积分动作 ", readConverterExp = "0=增加,1=减少")
    private String type;

    /** 备注 */
    @Excel(name = "备注", sort = 9)
    private String remark;

    /** 项目时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目时间", width = 30, dateFormat = "yyyy-MM-dd", sort = 4)
    private Date projectTime;

    /** 用户昵称 */
    @Excel(name = "教师名", sort = 1)
    private String nickName;

    /** 部门名称 */
    @Excel(name = "部门名称", sort = 2)
    private String deptName;

    /** 项目名称 */
    @Excel(name = "项目名称", sort = 3)
    private String projectName;

    /** 规则名称 */
    @Excel(name = "积分规则", sort = 7)
    private String ruleName;

    /** 项目类型 */
    @Excel(name = "项目类型", readConverterExp = "0=个人,1=团队", sort = 5)
    private String isTeam;

    /** 是否为团队负责人 */
    @Excel(name = "是否为团队负责人", readConverterExp = "0=否,1=是", sort = 6)
    private String isLeader;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }
    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }
    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setProjectTime(Date projectTime) 
    {
        this.projectTime = projectTime;
    }

    public Date getProjectTime() 
    {
        return projectTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getIsTeam() {
        return isTeam;
    }

    public void setIsTeam(String isTeam) {
        this.isTeam = isTeam;
    }

    public String getIsLeader() {
        return isLeader;
    }

    public void setIsLeader(String isLeader) {
        this.isLeader = isLeader;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("deptId", getDeptId())
            .append("projectId", getProjectId())
            .append("ruleId", getRuleId())
            .append("score", getScore())
            .append("type", getType())
            .append("remark", getRemark())
            .append("projectTime", getProjectTime())
            .append("createTime", getCreateTime())
            .toString();
    }
}
