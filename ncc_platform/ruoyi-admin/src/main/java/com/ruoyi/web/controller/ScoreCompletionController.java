package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.web.domain.Project2scoreRecord;
import com.ruoyi.web.domain.vo.ScoreCompletionVO;
import com.ruoyi.web.service.IScoreCompletionService;

/**
 * 积分完成情况Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/scoreCompletion")
public class ScoreCompletionController extends BaseController
{
    @Autowired
    private IScoreCompletionService scoreCompletionService;

    /**
     * 查询积分完成情况列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ScoreCompletionVO scoreCompletion)
    {
        startPage();
        // 调试输出，确认是否接收到参数
        System.out.println("接收到的参数 - excessCompletionRatio: " + scoreCompletion.getExcessCompletionRatio());
        System.out.println("接收到的参数 - job: " + scoreCompletion.getJob());
        System.out.println("接收到的参数 - ranks: " + scoreCompletion.getRanks());
        System.out.println("接收到的参数 - no: " + scoreCompletion.getNo());
        
        List<ScoreCompletionVO> list = scoreCompletionService.selectScoreCompletionList(scoreCompletion);
        return getDataTable(list);
    }

    /**
     * 查询用户积分明细
     */
    @GetMapping("/details")
    public TableDataInfo details(Long userId, Project2scoreRecord record)
    {
        startPage();
        List<Project2scoreRecord> list = scoreCompletionService.selectScoreDetailsList(userId, record);
        return getDataTable(list);
    }

    /**
     * 导出积分完成情况列表
     */
    @Log(title = "积分完成情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScoreCompletionVO scoreCompletion)
    {
        List<ScoreCompletionVO> list = scoreCompletionService.selectScoreCompletionList(scoreCompletion);
        ExcelUtil<ScoreCompletionVO> util = new ExcelUtil<ScoreCompletionVO>(ScoreCompletionVO.class);
        util.exportExcel(response, list, "积分完成情况");
    }
} 