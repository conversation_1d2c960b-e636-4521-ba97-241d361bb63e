package com.ruoyi.web.service.impl;

import java.util.*;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.domain.*;
import com.ruoyi.web.mapper.LixiangApplicationMapper;
import com.ruoyi.web.service.ICompContestantService;
import com.ruoyi.web.service.ICompInstructorService;
import com.ruoyi.web.service.ICompetitionBaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.JiexiangApplicationMapper;
import com.ruoyi.web.service.IJiexiangApplicationService;

/**
 * 结项信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
@Service
public class JiexiangApplicationServiceImpl implements IJiexiangApplicationService 
{
    @Autowired
    private JiexiangApplicationMapper jiexiangApplicationMapper;

    @Autowired
    private LixiangApplicationMapper lixiangApplicationMapper;

    @Autowired
    private ICompetitionBaseInfoService contestBaseInfoService;

    @Autowired
    private ICompInstructorService compInstructorService;

    @Autowired
    private ICompContestantService compContestantService;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询结项信息
     * 
     * @param id 结项信息主键
     * @return 结项信息
     */
    @Override
    public JiexiangApplication selectJiexiangApplicationById(Long id)
    {
        return jiexiangApplicationMapper.selectJiexiangApplicationById(id);
    }

    /**
     * 查询结项信息列表
     * 
     * @param jiexiangApplication 结项信息
     * @return 结项信息
     */
    @Override
    public List<JiexiangApplication> selectJiexiangApplicationList(JiexiangApplication jiexiangApplication)
    {
        return jiexiangApplicationMapper.selectJiexiangApplicationList(jiexiangApplication);
    }

    /**
     * 新增结项信息
     * 
     * @param jiexiangApplication 结项信息
     * @return 结果
     */
    @Override
    public int insertJiexiangApplication(JiexiangApplication jiexiangApplication)
    {
        jiexiangApplication.setCreateTime(DateUtils.getNowDate());
        return jiexiangApplicationMapper.insertJiexiangApplication(jiexiangApplication);
    }

    /**
     * 修改结项信息
     * 
     * @param jiexiangApplication 结项信息
     * @return 结果
     */
    @Override
    public int updateJiexiangApplication(JiexiangApplication jiexiangApplication)
    {
        jiexiangApplication.setUpdateTime(DateUtils.getNowDate());
        return jiexiangApplicationMapper.updateJiexiangApplication(jiexiangApplication);
    }

    /**
     * 批量删除结项信息
     * 
     * @param ids 需要删除的结项信息主键
     * @return 结果
     */
    @Override
    public int deleteJiexiangApplicationByIds(Long[] ids)
    {
        return jiexiangApplicationMapper.deleteJiexiangApplicationByIds(ids);
    }

    /**
     * 删除结项信息信息
     * 
     * @param id 结项信息主键
     * @return 结果
     */
    @Override
    public int deleteJiexiangApplicationById(Long id)
    {
        return jiexiangApplicationMapper.deleteJiexiangApplicationById(id);
    }

    /**
     * 获取模板
     *
     * @param jiexiangApplication
     * @return
     */
    @Override
    public Map<String, Object> getDataList(JiexiangApplication jiexiangApplication) {
        //根据竞赛ID获取竞赛信息
        CompetitionBaseInfo competitionBaseInfo = contestBaseInfoService.selectCompetitionBaseInfoById(jiexiangApplication.getCompetitionId());
        //根据竞赛信息中创建人id获取创建人信息
        SysUser sysUser = userService.selectUserById(competitionBaseInfo.getCreateUserId());
        //根据竞赛ID获取竞赛指导老师信息
        CompInstructor compInstructor = new CompInstructor();
        compInstructor.setCompId(jiexiangApplication.getCompetitionId());
        List<CompInstructor> compInstructors = compInstructorService.selectCompInstructorList(compInstructor);
        //根据竞赛ID获取竞赛参赛成员信息
        CompContestantVO compContestant = new CompContestantVO();
        compContestant.setCompetitionId(jiexiangApplication.getCompetitionId());
        List<CompContestantVO> compContestants = compContestantService.selectCompContestantListPlus(compContestant);
        //根据竞赛id获取立项书信息
        LixiangApplication lixiangApplication = new LixiangApplication();
        lixiangApplication.setCompetitionId(jiexiangApplication.getCompetitionId());
        List<LixiangApplication> lixiangApplications = lixiangApplicationMapper.selectLixiangApplicationList(lixiangApplication);
        if (lixiangApplications.size() > 0){
            lixiangApplication = lixiangApplications.get(0);
        }else {
            throw new RuntimeException("系统中未找到立项阶段填写的立项书信息");
        }
        //根据竞赛id获取结项书信息
        List<JiexiangApplication> jiexiangApplications = jiexiangApplicationMapper.selectJiexiangApplicationList(jiexiangApplication);
        if (jiexiangApplications.size() > 0){
            jiexiangApplication = jiexiangApplications.get(0);
        }else {
            throw new RuntimeException("未找到结项书信息");
        }
        Map<String, Object> data = new HashMap<>();
        //竞赛基础信息内容填写
        data.put("项目名称",competitionBaseInfo.getName());
        data.put("赛项名称",competitionBaseInfo.getEventName());
        data.put("项目负责人",sysUser.getNickName());
        data.put("联系电话",sysUser.getPhonenumber());
        data.put("竞赛主办单位",competitionBaseInfo.getOrganizer());
        data.put("竞赛承办单位",competitionBaseInfo.getUndertaker());
        data.put("申报日期", DateUtil.format(new Date(), "yyyy年 MM月 dd日"));
        data.put("竞赛时间", DateUtil.format(competitionBaseInfo.getMatchTime(), "yyyy年 MM月 dd日"));
        data.put("竞赛地点", competitionBaseInfo.getMatchVenue());
        //处理竞赛级别
        switch (competitionBaseInfo.getLevel()){
            case 6:
                data.put("竞赛级别","√国际级     □国家级     □省级     □市级   □校级  ");
                break;
            case 5:
                data.put("竞赛级别","□国际级     √国家级     □省级     □市级   □校级  ");
                break;
            case 3:
                data.put("竞赛级别","□国际级     □国家级     √省级     □市级   □校级  ");
                break;
            case 2:
                data.put("竞赛级别","□国际级     □国家级     □省级     √市级   □校级  ");
                break;
            case 0:
                data.put("竞赛级别","□国际级     □国家级     □省级     □市级   √校级  ");
                break;
        }
        //处理主办单位类型
        switch (competitionBaseInfo.getOrganizationalType()){
            case "0":
                data.put("组织单位类型1","√政府行政部门                □教学指导委员会（政府部门下属事业单位）");
                data.put("组织单位类型2","□企业、行业协会");
                break;
            case "1":
                data.put("组织单位类型1","□政府行政部门                √教学指导委员会（政府部门下属事业单位）");
                data.put("组织单位类型2","□企业、行业协会");
                break;
            case "2":
                data.put("组织单位类型1","□政府行政部门                □教学指导委员会（政府部门下属事业单位）");
                data.put("组织单位类型2","√企业、行业协会");
                break;
        }
        //处理指导老师信息
        data.put("指导老师",compInstructors);
        //处理参赛选手信息
        // 遍历compContestants处理班级名称为 年级+专业+班级
        for (CompContestantVO contestant : compContestants) {
            //获取学号前两位作为年级
            String grade = contestant.getNo().substring(0, 2);
            String className = grade+ "级" + contestant.getMajor() + contestant.getClassName();
            contestant.setClassName(className);
        }
        data.put("参赛选手",compContestants);
        //文档主题
        data.put("竞赛项目背景",jiexiangApplication.getCompetitionBg());
        data.put("竞赛项目实施过程",jiexiangApplication.getCompetitionImplement());
        data.put("竞赛项目参赛过程",jiexiangApplication.getCompetitionContest());
        data.put("竞赛项目所取得的成果",jiexiangApplication.getCompetitionAchievement());
        data.put("竞赛项目现阶段存在的不足",jiexiangApplication.getCompetitionInsufficient());
        data.put("今后对竞赛项目的展望等",jiexiangApplication.getCompetitionNext());
        //竞赛成果
        ArrayList<JieXiangChengGuo> jieXiangChengGuos = new ArrayList<>();
        //判断比赛是不是团体赛
        if ((competitionBaseInfo.getType()%2) == 1){
            HashMap<String, String> nameHashMap = new HashMap<>();
            HashMap<String, String> awardHashMap = new HashMap<>();
            ArrayList<String> groupList = new ArrayList<>();
            for (int i =0;i<compContestants.size();i++){
                //从nameHashMap获取队伍的名字，如果没有则新建，如果有则添加
                if (nameHashMap.containsKey(compContestants.get(i).getGroup())){
                    nameHashMap.put(compContestants.get(i).getGroup(),nameHashMap.get(compContestants.get(i).getGroup())+"、"+compContestants.get(i).getName());
                }else {
                    nameHashMap.put(compContestants.get(i).getGroup(),compContestants.get(i).getName());
                    awardHashMap.put(compContestants.get(i).getGroup(),compContestants.get(i).getAwards());
                    groupList.add(compContestants.get(i).getGroup());
                }
            }
            for (String group : groupList){
                JieXiangChengGuo jieXiangChengGuo = new JieXiangChengGuo();
                jieXiangChengGuo.setId(Integer.parseInt(group));
                jieXiangChengGuo.setItemName(jiexiangApplication.getItemName());
                jieXiangChengGuo.setAwardMax(jiexiangApplication.getMaxAward());
                jieXiangChengGuo.setAwardDate(jiexiangApplication.getAwardTime());
                jieXiangChengGuo.setProjectLevel(jiexiangApplication.getAwardLevel());
                jieXiangChengGuo.setProjectType("团队赛");
                String awards = awardHashMap.get(group);
                if ("0".equals(awards)){
                    jieXiangChengGuo.setAward("优秀奖");
                }
                if ("1".equals(awards)){
                    jieXiangChengGuo.setAward("三等奖");
                }
                if ("2".equals(awards)){
                    jieXiangChengGuo.setAward("二等奖");
                }
                if ("3".equals(awards)){
                    jieXiangChengGuo.setAward("一等奖");
                }
                if ("4".equals(awards)){
                    jieXiangChengGuo.setAward("特等奖");
                }
                if ("-1".equals(awards)){
                    jieXiangChengGuo.setAward("未获奖");
                }
                jieXiangChengGuo.setAwardee(nameHashMap.get(group));
                jieXiangChengGuos.add(jieXiangChengGuo);
            }

        }else{
            for (int i = 0; i < compContestants.size(); i++) {
                CompContestantVO contestant = compContestants.get(i);
                JieXiangChengGuo jieXiangChengGuo = new JieXiangChengGuo();
                jieXiangChengGuo.setId(i+1);
                jieXiangChengGuo.setItemName(jiexiangApplication.getItemName());
                jieXiangChengGuo.setAwardMax(jiexiangApplication.getMaxAward());
                jieXiangChengGuo.setAwardDate(jiexiangApplication.getAwardTime());
                jieXiangChengGuo.setProjectLevel(jiexiangApplication.getAwardLevel());
                jieXiangChengGuo.setAwardee(contestant.getName());
                jieXiangChengGuo.setProjectType("个人赛");
                if ("0".equals(contestant.getAwards())){
                    jieXiangChengGuo.setAward("优秀奖");
                }
                if ("1".equals(contestant.getAwards())){
                    jieXiangChengGuo.setAward("三等奖");
                }
                if ("2".equals(contestant.getAwards())){
                    jieXiangChengGuo.setAward("二等奖");
                }
                if ("3".equals(contestant.getAwards())){
                    jieXiangChengGuo.setAward("一等奖");
                }
                if ("4".equals(contestant.getAwards())){
                    jieXiangChengGuo.setAward("特等奖");
                }
                if ("-1".equals(contestant.getAwards())){
                    jieXiangChengGuo.setAward("未获奖");
                }
                jieXiangChengGuos.add(jieXiangChengGuo);
            }
        }
        data.put("竞赛成果",jieXiangChengGuos);
        //经费预算表
        data.put("研发人员劳务",lixiangApplication.getCost0());
        data.put("管理人员劳务",lixiangApplication.getCost1());
        data.put("耗材费",lixiangApplication.getCost2());
        data.put("仪器设备使用费",lixiangApplication.getCost3());
        data.put("专用设备购置",lixiangApplication.getCost4());
        data.put("实验室改造",lixiangApplication.getCost5());
        data.put("实验材料费",lixiangApplication.getCost6());
        data.put("测试及试验费",lixiangApplication.getCost7());
        data.put("学生参与活动的学生交通费",lixiangApplication.getCost8());
        data.put("教师差旅费",lixiangApplication.getCost9());
        data.put("非图书资料费",lixiangApplication.getCost10());
        data.put("出版费",lixiangApplication.getCost11());
        data.put("咨询",lixiangApplication.getCost12());
        data.put("会议费",lixiangApplication.getCost13());
        data.put("图书",lixiangApplication.getCost14());
        data.put("办公费",lixiangApplication.getCost15());
        data.put("学生竞赛费用",lixiangApplication.getCost16());
        data.put("报名考试",lixiangApplication.getCost17());
        data.put("其他费用1金额",lixiangApplication.getCost18());
        data.put("其他费用2金额",lixiangApplication.getCost19());
        data.put("其他费用3金额",lixiangApplication.getCost20());
        data.put("其他费用1",lixiangApplication.getCost18Name());
        data.put("其他费用2",lixiangApplication.getCost19Name());
        data.put("其他费用3",lixiangApplication.getCost20Name());
        data.put("经费支出合计",lixiangApplication.getCost21());
        //经费决算表
        data.put("决算研发人员劳务",jiexiangApplication.getCost0());
        data.put("决算管理人员劳务",jiexiangApplication.getCost1());
        data.put("决算耗材费",jiexiangApplication.getCost2());
        data.put("决算仪器设备使用费",jiexiangApplication.getCost3());
        data.put("决算专用设备购置",jiexiangApplication.getCost4());
        data.put("决算实验室改造",jiexiangApplication.getCost5());
        data.put("决算实验材料费",jiexiangApplication.getCost6());
        data.put("决算测试及试验费",jiexiangApplication.getCost7());
        data.put("决算学生参与活动的学生交通费",jiexiangApplication.getCost8());
        data.put("决算教师差旅费",jiexiangApplication.getCost9());
        data.put("决算非图书资料费",jiexiangApplication.getCost10());
        data.put("决算出版费",jiexiangApplication.getCost11());
        data.put("决算咨询",jiexiangApplication.getCost12());
        data.put("决算会议费",jiexiangApplication.getCost13());
        data.put("决算图书",jiexiangApplication.getCost14());
        data.put("决算办公费",jiexiangApplication.getCost15());
        data.put("决算学生竞赛费用",jiexiangApplication.getCost16());
        data.put("决算报名考试",jiexiangApplication.getCost17());
        data.put("决算其他费用1金额",jiexiangApplication.getCost18());
        data.put("决算其他费用2金额",jiexiangApplication.getCost19());
        data.put("决算其他费用3金额",jiexiangApplication.getCost20());
        data.put("决算经费支出合计",jiexiangApplication.getCost21());
        return data;
    }
}
