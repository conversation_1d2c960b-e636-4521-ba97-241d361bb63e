package com.ruoyi.web.service.impl;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.web.domain.CompContestantDTO;
import com.ruoyi.web.domain.CompContestantVO;
import com.ruoyi.web.domain.StudentInfo;
import com.ruoyi.web.mapper.StudentInfoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.web.mapper.CompContestantMapper;
import com.ruoyi.web.domain.CompContestant;
import com.ruoyi.web.service.ICompContestantService;

import javax.validation.Validator;


/**
 * 竞赛选手Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public class CompContestantServiceImpl implements ICompContestantService 
{
    @Autowired
    private CompContestantMapper compContestantMapper;

    @Autowired
    private StudentInfoMapper   studentInfoMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    protected Validator validator;
    private static final Logger log = LoggerFactory.getLogger(StudentInfoServiceImpl.class);

    /**
     * 查询竞赛选手
     * 
     * @param id 竞赛选手主键
     * @return 竞赛选手
     */
    @Override
    public CompContestant selectCompContestantById(Long id)
    {
        return compContestantMapper.selectCompContestantById(id);
    }

    /**
     * 查询竞赛选手列表
     * 
     * @param compContestant 竞赛选手
     * @return 竞赛选手
     */
    @Override
    public List<CompContestant> selectCompContestantList(CompContestant compContestant)
    {
        return compContestantMapper.selectCompContestantList(compContestant);
    }
    /**
     * 查询竞赛选手列表
     *
     * @param compContestant 竞赛选手
     * @return 竞赛选手
     */
    @Override
    public List<CompContestantVO> selectCompContestantListPro(CompContestantVO compContestant)
    {
        List<CompContestantVO> compContestantVOS = compContestantMapper.selectCompContestantListPlus(compContestant);
        return fullInfo(compContestantVOS);
    }

    /**
     * 查询竞赛选手列表
     * @param compContestant
     * @return
     */
    @Override
    public List<CompContestantVO> selectCompContestantListPlus(CompContestantVO compContestant) {
        List<CompContestantVO> compContestantVOS = compContestantMapper.selectCompContestantListPlus(compContestant);
        return fullInfo(compContestantVOS);
    }

    /**
     * 填充选手信息 TODO 优化
     * @param compContestants
     * @return
     */

    public List<CompContestantVO> fullInfo(List<CompContestantVO> compContestants){
        for (CompContestantVO temp : compContestants) {
            if (temp.getType()==0){//当前查询信息为学生
                //TODO 优化，查询次数实在是太多，如果有三十个选手难不成真查三十次？需要的请求连接数目岂不是太多了
                // 查询学生信息
                StudentInfo studentInfo = studentInfoMapper.selectStudentInfoById(Long.valueOf(temp.getContestantId()));
                if (studentInfo!=null){
                    temp.setName(studentInfo.getName());
                    temp.setNo(studentInfo.getNo());
                    temp.setMajor(studentInfo.getMajor());
                    temp.setClassName(studentInfo.getClassName());
                    temp.setGender(studentInfo.getGender());
                }
            }else {// 当前查询信息为老师
                //TODO 优化优化
                SysUser sysUser = userMapper.selectUserById(Long.valueOf(temp.getContestantId()));
                if (sysUser!=null){
                    temp.setName(sysUser.getNickName()+"(教师)");
                    temp.setNo(sysUser.getNo());
                    temp.setDeptName(sysUser.getDept().getDeptName());
                    temp.setMajor(sysUser.getDept().getDeptName());
                    temp.setClassName("/");
                    //如果是男就setGender为1如果为女则setGender为2
                    if (sysUser.getSex().equals("男")){
                        temp.setGender(1);
                    }else {
                        temp.setGender(2);
                    }
                }
            }
        }
        return compContestants;
    }

    /**
     * 新增竞赛选手
     * 
     * @param compContestant 竞赛选手
     * @return 结果
     */
    @Override
    public int insertCompContestant(CompContestantDTO compContestant)
    {
        //判断数据是否已经存在
        QueryWrapper<CompContestant> compContestantQueryWrapper = new QueryWrapper<>();
        compContestantQueryWrapper.eq("competition_id",compContestant.getCompetitionId());
        compContestantQueryWrapper.eq("no",compContestant.getNo());
        compContestantQueryWrapper.eq("contestant_role",compContestant.getContestantRole());
        if(compContestantMapper.selectCount(compContestantQueryWrapper)>0){
            throw new ServiceException("该选手已存在，请勿重复添加！");
        }
        CompContestant result = new CompContestant();
        //传入数据判断是老师还是学生，如果是学生需要判断是否存在，如果存在则更新原有数据，如果不存在则新增
        if(compContestant.getType() == 0){
            StudentInfo studentInfo = new StudentInfo();
            BeanUtil.copyProperties(compContestant,studentInfo);
            if (StringUtils.isNotEmpty(studentInfo.getName())){
                List<StudentInfo> studentInfos = studentInfoMapper.selectStudentInfoList(studentInfo);
                if(studentInfos.size()>0){
                    StudentInfo info = studentInfos.get(0);
                    studentInfo.setId(info.getId());
                }
            }else {
                studentInfo.setCreateTime(DateUtils.getNowDate());
            }

            if(studentInfo.getId() !=null&&studentInfo.getId()!= 0){
                studentInfo.setUpdateTime(DateUtils.getNowDate());
                studentInfoMapper.updateStudentInfo(studentInfo);
            }else{
                studentInfo.setCreateTime(DateUtils.getNowDate());
                studentInfoMapper.insertStudentInfo(studentInfo);
            }
            result.setContestantId(String.valueOf(studentInfo.getId()));
        }else {
            result.setContestantId(compContestant.getContestantId());
        }
        result.setMajor(compContestant.getMajor());
        result.setType(compContestant.getType());
        result.setNo(compContestant.getNo());
        result.setGroup(compContestant.getGroup());
        result.setContestantRole(compContestant.getContestantRole());
        result.setCompetitionId(compContestant.getCompetitionId());
        return compContestantMapper.insertCompContestant(result);
    }

    /**
     * 修改竞赛选手
     * 
     * @param compContestant 竞赛选手
     * @return 结果
     */
    @Override
    public int updateCompContestant(CompContestant compContestant)
    {
        return compContestantMapper.updateCompContestant(compContestant);
    }

    /**
     * 批量删除竞赛选手
     * 
     * @param ids 需要删除的竞赛选手主键
     * @return 结果
     */
    @Override
    public int deleteCompContestantByIds(Long[] ids)
    {
        return compContestantMapper.deleteCompContestantByIds(ids);
    }

    /**
     * 删除竞赛选手信息
     * 
     * @param id 竞赛选手主键
     * @return 结果
     */
    @Override
    public int deleteCompContestantById(Long id)
    {
        return compContestantMapper.deleteCompContestantById(id);
    }



    @Override
    public String importCompContestant(List<CompContestantDTO> studentList, String operName, Long competitionId,Long competitionType) {
        if (StringUtils.isNull(studentList) || studentList.size() == 0)
        {
            throw new ServiceException("导入参赛选手数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        // 输入数据导入流程，先判断有没有，再判断是否添加
        for (CompContestantDTO studentInfo : studentList)
        {
            if(studentInfo.getNo() == null || studentInfo.getNo().isEmpty()){
                String errorMessage = "导入失败：请填写学生信息中“学号”！";
                return errorMessage.toString();
            }
            if(studentInfo.getName() == null || studentInfo.getName().isEmpty()){
                String errorMessage = "导入失败：请填写学生信息中“姓名”！";
                return errorMessage.toString();
            }
            if(studentInfo.getMajor() == null || studentInfo.getMajor().isEmpty()){
                String errorMessage = "导入失败：请填写学生信息中“专业”！";
                return errorMessage.toString();
            }
            if(studentInfo.getClassName() == null || studentInfo.getClassName().isEmpty()){
                String errorMessage = "导入失败：请填写学生信息中“班级”！";
                return errorMessage.toString();
            }
            if(studentInfo.getGender() == null){
                String errorMessage = "导入失败：请填写学生信息中“性别”！";
                return errorMessage.toString();
            }
            if(competitionType == 1 || competitionType == 3){
                if(studentInfo.getGroup() == null || studentInfo.getGroup().isEmpty()){
                    String errorMessage = "导入失败：请填写学生信息中“队伍”！";
                    return errorMessage.toString();
                }
            }
            try
            {
                //验证选手是否在竞赛中，如果在数据库中则抛出异常
                QueryWrapper<CompContestant> compContestantQueryWrapper = new QueryWrapper<>();
                compContestantQueryWrapper.eq("competition_id",competitionId);
                compContestantQueryWrapper.eq("no",studentInfo.getNo());
                compContestantQueryWrapper.eq("contestant_role",studentInfo.getContestantRole());
                if(compContestantMapper.selectCount(compContestantQueryWrapper)>0){
                    throw new ServiceException("该选手已存在，请勿重复添加！");
                }
                //验证选手是否在数据库中，如果在数据库则直接添加选手
                QueryWrapper<StudentInfo> studentInfoQueryWrapper = new QueryWrapper<>();
                studentInfoQueryWrapper.eq("no",studentInfo.getNo()).eq("name",studentInfo.getName()).
                        eq("major",studentInfo.getMajor());
                List<StudentInfo> studentInfos = studentInfoMapper.selectList(studentInfoQueryWrapper);
                //改成竞赛选手信息
                CompContestant compContestant = new CompContestant();
                compContestant.setCompetitionId(competitionId);
                compContestant.setMajor(studentInfo.getMajor());
                compContestant.setContestantRole(studentInfo.getContestantRole());
                compContestant.setType(0);//设置为学生
                compContestant.setGroup(studentInfo.getGroup());
                if(studentInfos.size()>0){//数据库中存在
                    StudentInfo info = studentInfos.get(0);
                    compContestant.setContestantId(String.valueOf(info.getId()));
                    info.setUpdateTime(DateUtils.getNowDate());
                    compContestant.setNo(info.getNo());
                    studentInfoMapper.updateStudentInfo(info);
                }else{//数据库中不存在
                    BeanValidators.validateWithException(validator, studentInfo);
                    StudentInfo info = new StudentInfo();
                    BeanUtil.copyProperties(studentInfo, info);
                    info.setCreateTime(DateUtils.getNowDate());
                    //判断专业与班级是否为空，如果为空则抛出异常
                    if(StringUtils.isEmpty(studentInfo.getMajor())){
                        throw new ServiceException("请填写学生信息中“专业”！");
                    }
                    if(StringUtils.isEmpty(studentInfo.getClassName())){
                        throw new ServiceException("请填写学生信息中“班级”！");
                    }
                    studentInfoMapper.insertStudentInfo(info);
                    compContestant.setNo(studentInfo.getNo());
                    compContestant.setContestantId(String.valueOf(info.getId()));
                }

                compContestantMapper.insertCompContestant(compContestant);
                successNum++;
                successMsg.append("<br/>" + successNum + "、选手 " + studentInfo.getName() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、选手 " + studentInfo.getName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public int updateCompContestants(List<CompContestant> compContestants) {
        int result = 0;
        //遍历 copmContestants
        for (CompContestant compContestant : compContestants) {
            int i = compContestantMapper.updateCompContestant(compContestant);
            result+=i;
        }
        return result;
    }
}
