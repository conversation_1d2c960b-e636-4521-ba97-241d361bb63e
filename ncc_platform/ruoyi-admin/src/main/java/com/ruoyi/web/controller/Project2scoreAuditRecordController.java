package com.ruoyi.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.domain.Project2scoreAuditRecord;
import com.ruoyi.web.service.IProject2scoreAuditRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.web.domain.Project2score;
import com.ruoyi.web.service.IProject2scoreService;
import com.ruoyi.web.domain.vo.Project2scoreAuditExportVO;

/**
 * 审核记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/web/auditRecord")
public class Project2scoreAuditRecordController extends BaseController
{
    @Autowired
    private IProject2scoreAuditRecordService project2scoreAuditRecordService;

    @Autowired
    private IProject2scoreService project2scoreService;

    /**
     * 查询审核记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Project2scoreAuditRecord project2scoreAuditRecord)
    {
        startPage();
        List<Project2scoreAuditRecord> list = project2scoreAuditRecordService.selectProject2scoreAuditRecordList(project2scoreAuditRecord);
        return getDataTable(list);
    }


    /**
     * 获取审核记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('web:auditRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(project2scoreAuditRecordService.selectProject2scoreAuditRecordById(id));
    }

    /**
     * 新增审核记录
     */
    @PreAuthorize("@ss.hasPermi('web:auditRecord:add')")
    @Log(title = "审核记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Project2scoreAuditRecord project2scoreAuditRecord)
    {
        return toAjax(project2scoreAuditRecordService.insertProject2scoreAuditRecord(project2scoreAuditRecord));
    }

    /**
     * 修改审核记录
     */
    @PreAuthorize("@ss.hasPermi('web:auditRecord:edit')")
    @Log(title = "审核记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Project2scoreAuditRecord project2scoreAuditRecord)
    {
        return toAjax(project2scoreAuditRecordService.updateProject2scoreAuditRecord(project2scoreAuditRecord));
    }

    /**
     * 删除审核记录
     */
    @PreAuthorize("@ss.hasPermi('web:auditRecord:remove')")
    @Log(title = "审核记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(project2scoreAuditRecordService.deleteProject2scoreAuditRecordByIds(ids));
    }

    /**
     * 审批项目积分
     */
    @PreAuthorize("@ss.hasPermi('web:auditRecord:edit')")
    @Log(title = "项目积分审批", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult audit(@RequestBody Project2scoreAuditRecord auditRecord)
    {
        auditRecord.setCreateTime(DateUtils.getNowDate());
        auditRecord.setCreateBy(getUsername());
        return toAjax(project2scoreAuditRecordService.auditProject2score(auditRecord));
    }

    /**
     * 导出项目积分审核记录
     */
    @PreAuthorize("@ss.hasPermi('web:project2score:export')")
    @Log(title = "项目积分审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Project2score project2score)
    {
        List<Project2scoreAuditExportVO> list = project2scoreService.selectProject2scoreAuditExportList(project2score);
        ExcelUtil<Project2scoreAuditExportVO> util = new ExcelUtil<Project2scoreAuditExportVO>(Project2scoreAuditExportVO.class);
        util.exportExcel(response, list, "项目积分审核记录");
    }
}
