package com.ruoyi.web.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 竞赛信息基础信息对象 competition_base_info
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "competition_base_info")
@Data
@ApiModel(value = "CompetitionBaseInfo", description = "竞赛信息基础信息对象")
public class CompetitionBaseInfo extends BaseEntity
{
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /** 比赛ID */
    @Excel(name = "比赛ID")
    @ApiModelProperty(value = "比赛ID", example = "比赛ID",required = true)
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 比赛名称 */
    @Excel(name = "比赛名称")
    @ApiModelProperty(value = "比赛名称", example = "比赛名称",required = true)
    private String name;


    /** 赛项名称 */
    @Excel(name = "赛项名称")
    @ApiModelProperty(value = "赛项名称", example = "赛项名称",required = true)
    private String eventName;

    /** 创建人ID */
    @Excel(name = "申报人",dictType = "sys_user")
    @ApiModelProperty(value = "创建人ID", example = "创建人ID",required = true)
    private Long createUserId;

    @Excel(name = "申报人部门",dictType = "sys_dept")
    @ApiModelProperty(value = "创建人部门ID", example = "创建人部门ID",required = true)
    private Long createUserDept;


    /** 竞赛级别 */
    @Excel(name = "竞赛级别",dictType = "comp_level")
    @ApiModelProperty(value = "竞赛级别", example = "竞赛级别",required = true)
    private Integer level;

    /** 竞赛类型 */
    @Excel(name = "竞赛类型",dictType = "comp_type")
    @ApiModelProperty(value = "竞赛类型", example = "竞赛类型",required = true)
    private Integer type;

    /** 竞赛类别 */
    @Excel(name = "竞赛类别",dictType = "comp_category")
    @ApiModelProperty(value = "竞赛类别", example = "竞赛类别",required = true)
    private Integer category;

    /** 主办方 */
    @Excel(name = "主办方")
    @ApiModelProperty(value = "主办方", example = "主办方",required = true)
    private String organizer;

    /** 承办方 */
    @Excel(name = "承办方")
    @ApiModelProperty(value = "承办方",example = "承办方",required = true)
    private String undertaker;

    /** 比赛时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "比赛时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "比赛时间", example = "比赛时间",required = true)
    private Date matchTime;

    /** 比赛地点 */
    @Excel(name = "比赛地点")
    @ApiModelProperty(value = "比赛地点", example = "比赛地点",required = true)
    private String matchVenue;

    /** 立项时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "立项时间", width = 30, dateFormat = "yyyy-MM-dd" , defaultValue = "暂未立项")
    @ApiModelProperty(value = "立项时间", example = "立项时间",required = true)
    private Date approvalTime;

    /** 结项时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结项时间", width = 30, dateFormat = "yyyy-MM-dd" , defaultValue = "暂未结项")
    @ApiModelProperty(value = "结项时间", example = "结项时间",required = true)
    private Date closeTime;

    /** 立项文件 */
    private String approvalFile;

    /** 结项文件 */
    private String closeFile;

    /** 附件文件 */
    private String attachmentFile;

    /** 组织单位类型 */
    @Excel(name = "组织单位类型",dictType = "organizational_type")
    @ApiModelProperty(value = "组织单位类型", example = "组织单位类型",required = true)
    private String organizationalType;


    /** 状态 **/
    @Excel(name = "状态",dictType = "match_status")
    @ApiModelProperty(value = "状态", example = "状态",required = true)
    private Integer status;
    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "备注",required = true)
    private String remarks;


}
