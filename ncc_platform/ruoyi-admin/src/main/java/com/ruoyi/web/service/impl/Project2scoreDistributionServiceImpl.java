package com.ruoyi.web.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.web.mapper.Project2scoreDistributionMapper;
import com.ruoyi.web.domain.Project2scoreDistribution;
import com.ruoyi.web.service.IProject2scoreDistributionService;
import com.ruoyi.web.domain.vo.Project2scoreDistributionVO;
import com.ruoyi.web.domain.vo.Project2scoreDistributionExportVO;

/**
 * 积分分配Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class Project2scoreDistributionServiceImpl implements IProject2scoreDistributionService 
{
    @Autowired
    private Project2scoreDistributionMapper project2scoreDistributionMapper;

    /**
     * 查询积分分配
     * 
     * @param id 积分分配主键
     * @return 积分分配
     */
    @Override
    public Project2scoreDistribution selectProject2scoreDistributionById(Long id)
    {
        return project2scoreDistributionMapper.selectProject2scoreDistributionById(id);
    }

    /**
     * 查询积分分配列表
     * 
     * @param project2scoreDistribution 积分分配
     * @return 积分分配
     */
    @Override
    public List<Project2scoreDistribution> selectProject2scoreDistributionList(Project2scoreDistribution project2scoreDistribution)
    {
        return project2scoreDistributionMapper.selectProject2scoreDistributionList(project2scoreDistribution);
    }

    /**
     * 查询当前用户参与的团队项目积分分配列表
     * 
     * @param project2scoreDistribution 积分分配
     * @return 积分分配集合
     */
    @Override
    public List<Project2scoreDistributionVO> selectMyDistributionList(Project2scoreDistributionVO project2scoreDistribution)
    {
        return project2scoreDistributionMapper.selectMyDistributionList(project2scoreDistribution);
    }

    /**
     * 新增积分分配
     * 
     * @param project2scoreDistribution 积分分配
     * @return 结果
     */
    @Override
    public int insertProject2scoreDistribution(Project2scoreDistribution project2scoreDistribution)
    {
        project2scoreDistribution.setCreateTime(DateUtils.getNowDate());
        return project2scoreDistributionMapper.insertProject2scoreDistribution(project2scoreDistribution);
    }

    /**
     * 修改积分分配
     * 
     * @param project2scoreDistribution 积分分配
     * @return 结果
     */
    @Override
    public int updateProject2scoreDistribution(Project2scoreDistribution project2scoreDistribution)
    {
        project2scoreDistribution.setUpdateTime(DateUtils.getNowDate());
        return project2scoreDistributionMapper.updateProject2scoreDistribution(project2scoreDistribution);
    }

    /**
     * 批量删除积分分配
     * 
     * @param ids 需要删除的积分分配主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreDistributionByIds(Long[] ids)
    {
        return project2scoreDistributionMapper.deleteProject2scoreDistributionByIds(ids);
    }

    /**
     * 删除积分分配信息
     * 
     * @param id 积分分配主键
     * @return 结果
     */
    @Override
    public int deleteProject2scoreDistributionById(Long id)
    {
        return project2scoreDistributionMapper.deleteProject2scoreDistributionById(id);
    }

    /**
     * 批量保存积分分配
     *
     * @param distributions 积分分配列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDistributions(List<Project2scoreDistribution> distributions)
    {
        if (distributions == null || distributions.isEmpty()) {
            return 0;
        }

        // 先删除原有的分配记录
        Long projectId = distributions.get(0).getProjectId();
        Project2scoreDistribution query = new Project2scoreDistribution();
        query.setProjectId(projectId);
        List<Project2scoreDistribution> oldList = project2scoreDistributionMapper.selectProject2scoreDistributionList(query);
        if (!oldList.isEmpty()) {
            Long[] ids = oldList.stream().map(Project2scoreDistribution::getId).toArray(Long[]::new);
            project2scoreDistributionMapper.deleteProject2scoreDistributionByIds(ids);
        }

        // 批量插入新的分配记录
        int rows = 0;
        for (Project2scoreDistribution distribution : distributions) {
            distribution.setCreateTime(DateUtils.getNowDate());
            rows += project2scoreDistributionMapper.insertProject2scoreDistribution(distribution);
        }
        return rows;
    }

    /**
     * 删除项目的所有团队成员
     */
    @Override
    public int deleteByProjectId(Long projectId) {
        return project2scoreDistributionMapper.deleteByProjectId(projectId);
    }

    /**
     * 根据项目ID查询团队成员分配记录
     * 
     * @param projectId 项目ID
     * @return 团队成员分配记录列表
     */
    @Override
    public List<Project2scoreDistribution> selectProject2scoreDistributionByProjectId(Long projectId)
    {
        return project2scoreDistributionMapper.selectProject2scoreDistributionByProjectId(projectId);
    }

    /**
     * 批量更新项目团队成员分配记录
     * 
     * @param projectId 项目ID
     * @param members 团队成员列表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateProject2scoreDistributionBatch(Long projectId, List<Project2scoreDistribution> members)
    {
        // 1. 删除原有的分配记录
        project2scoreDistributionMapper.deleteByProjectId(projectId);
        
        // 2. 批量插入新的分配记录
        if (members != null && !members.isEmpty())
        {
            return project2scoreDistributionMapper.batchInsert(members);
        }
        
        return 1;
    }

    /**
     * 查询导出数据列表
     * 
     * @param project2scoreDistribution 查询条件
     * @return 导出数据列表
     */
    @Override
    public List<Project2scoreDistributionExportVO> selectDistributionExportList(Project2scoreDistributionVO project2scoreDistribution)
    {
        return project2scoreDistributionMapper.selectDistributionExportList(project2scoreDistribution);
    }

}
