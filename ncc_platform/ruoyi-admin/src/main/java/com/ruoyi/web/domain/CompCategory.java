package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.TreeEntity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 竞赛类别对象 comp_category
 * 
 * <AUTHOR>
 * @date 2024-04-22
 */
@Data
@TableName(value = "comp_category")
@ApiModel(value = "CompCategory", description = "竞赛类别对象")
public class CompCategory implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /** 竞赛类型ID */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "竞赛类型ID", required = true)
    @Excel(name = "竞赛类型ID")
    private Long id;

    /** 竞赛类型 */
    @Excel(name = "竞赛类型")
    @ApiModelProperty(value = "竞赛类型", example = "竞赛类型",required = true)
    private String name;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态", example = "状态",required = true)
    private Long status;

    /** 父节点ID */
    @Excel(name = "父节点ID")
    @ApiModelProperty(value = "父节点ID", example = "父节点ID",required = true)
    private Long fatherId;

    @TableField(exist = false)
    @ApiModelProperty(value = "子节点", required = true)
    private List<CompCategory> children;

}
