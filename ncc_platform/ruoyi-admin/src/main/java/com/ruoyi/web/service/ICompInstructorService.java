package com.ruoyi.web.service;

import java.util.List;
import com.ruoyi.web.domain.CompInstructor;
import com.ruoyi.web.domain.CompInstructorVO;
import org.springframework.stereotype.Service;

/**
 * 竞赛指导教师Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public interface ICompInstructorService 
{
    /**
     * 查询竞赛指导教师
     * 
     * @param id 竞赛指导教师主键
     * @return 竞赛指导教师
     */
    public CompInstructor selectCompInstructorById(Long id);

    /**
     * 查询竞赛指导教师列表
     * 
     * @param compInstructor 竞赛指导教师
     * @return 竞赛指导教师集合
     */
    public List<CompInstructor> selectCompInstructorList(CompInstructor compInstructor);

    /**
     * 新增竞赛指导教师
     * 
     * @param compInstructor 竞赛指导教师
     * @return 结果
     */
    public int insertCompInstructor(CompInstructor compInstructor);

    /**
     * 修改竞赛指导教师
     * 
     * @param compInstructor 竞赛指导教师
     * @return 结果
     */
    public int updateCompInstructor(CompInstructor compInstructor);

    /**
     * 批量删除竞赛指导教师
     * 
     * @param ids 需要删除的竞赛指导教师主键集合
     * @return 结果
     */
    public int deleteCompInstructorByIds(Long[] ids);

    /**
     * 删除竞赛指导教师信息
     * 
     * @param id 竞赛指导教师主键
     * @return 结果
     */
    public int deleteCompInstructorById(Long id);

    List<CompInstructorVO> selectCompInstructorListPlus(CompInstructor compInstructor);
}
