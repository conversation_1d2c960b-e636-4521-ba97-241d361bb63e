package com.ruoyi.web.service;

import java.util.List;
import com.ruoyi.web.domain.CompCategory;

/**
 * 竞赛类别Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-22
 */
public interface ICompCategoryService 
{
    /**
     * 查询竞赛类别
     * 
     * @param id 竞赛类别主键
     * @return 竞赛类别
     */
    public CompCategory selectCompCategoryById(Long id);

    /**
     * 查询竞赛类别列表
     * 
     * @param compCategory 竞赛类别
     * @return 竞赛类别集合
     */
    public List<CompCategory> selectCompCategoryList(CompCategory compCategory);

    /**
     * 新增竞赛类别
     * 
     * @param compCategory 竞赛类别
     * @return 结果
     */
    public int insertCompCategory(CompCategory compCategory);

    /**
     * 修改竞赛类别
     * 
     * @param compCategory 竞赛类别
     * @return 结果
     */
    public int updateCompCategory(CompCategory compCategory);

    /**
     * 批量删除竞赛类别
     * 
     * @param ids 需要删除的竞赛类别主键集合
     * @return 结果
     */
    public int deleteCompCategoryByIds(Long[] ids);

    /**
     * 删除竞赛类别信息
     * 
     * @param id 竞赛类别主键
     * @return 结果
     */
    public int deleteCompCategoryById(Long id);

    /**
     * 查询竞赛类别列表 4Tree
     *
     * @param compCategory 竞赛类别
     * @return 竞赛类别集合
     */
    public List<CompCategory> selectCompCategoryList4Tree(CompCategory compCategory);
}
