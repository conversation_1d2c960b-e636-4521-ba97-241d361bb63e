package com.ruoyi.web.domain.vo;

import com.ruoyi.web.domain.Project2scoreDistribution;
import java.util.Date;

/**
 * 积分分配视图对象
 * 
 * <AUTHOR>
 */
public class Project2scoreDistributionVO extends Project2scoreDistribution
{
    private static final long serialVersionUID = 1L;

    /** 项目名称 */
    private String projectName;

    /** 项目状态 */
    private String status;

    /** 项目时间 */
    private Date projectTime;

    /** 项目开始时间 */
    private Date projectTimeStart;

    /** 项目结束时间 */
    private Date projectTimeEnd;

    /** 规则ID */
    private Long ruleId;

    /** 用户名称 */
    private String userName;

    /** 部门名称 */
    private String deptName;

    /** 成员显示名称（院内用户显示用户名，外部人员显示memberName） */
    private String memberDisplayName;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getProjectTime() {
        return projectTime;
    }

    public void setProjectTime(Date projectTime) {
        this.projectTime = projectTime;
    }

    public Date getProjectTimeStart() {
        return projectTimeStart;
    }

    public void setProjectTimeStart(Date projectTimeStart) {
        this.projectTimeStart = projectTimeStart;
    }

    public Date getProjectTimeEnd() {
        return projectTimeEnd;
    }

    public void setProjectTimeEnd(Date projectTimeEnd) {
        this.projectTimeEnd = projectTimeEnd;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getMemberDisplayName() {
        if ("0".equals(this.getMemberType())) {
            return this.getUserName();
        }
        return this.getMemberName();
    }

    public void setMemberDisplayName(String memberDisplayName) {
        this.memberDisplayName = memberDisplayName;
    }
} 