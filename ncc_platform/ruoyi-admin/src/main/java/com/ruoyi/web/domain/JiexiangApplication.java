package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 结项信息对象 jiexiang_application
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
@ApiModel(value = "JiexiangApplication" ,description = "结项信息对象")
@Data
@TableName(value = "jiexiang_application")
public class JiexiangApplication extends BaseEntity
{
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /** 立项文件ID */
    @ApiModelProperty(value = "立项文件ID", required = true)
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 比赛ID */
    @Excel(name = "比赛ID")
    @ApiModelProperty(value = "比赛ID", required = true)
    private Long competitionId;

    /** 实际支出 */
    @Excel(name = "实际支出")
    @ApiModelProperty(value = "实际支出", required = true)
    private Double actualExpenditure;

    /** 竞赛项目背景（包括竞赛项目的特点、意义、历年参赛情况等） */
    @Excel(name = "竞赛项目背景", readConverterExp = "包=括竞赛项目的特点、意义、历年参赛情况等")
    @ApiModelProperty(value = "竞赛项目背景", required = true)
    private String competitionBg;

    /** 竞赛项目实施过程（包括竞赛项目的组织、培训、指导、现有条件利用等） */
    @ApiModelProperty(value = "竞赛项目实施过程", required = true)
    @Excel(name = "竞赛项目实施过程", readConverterExp = "包=括竞赛项目的组织、培训、指导、现有条件利用等")
    private String competitionImplement;

    /** 竞赛项目参赛过程 */
    @ApiModelProperty(value = "竞赛项目参赛过程", required = true)
    @Excel(name = "竞赛项目参赛过程")
    private String competitionContest;

    /** 竞赛项目所取得的成果（成绩、奖励等）及应用（对竞赛项目开展学生、教学的促进作用等） */
    @ApiModelProperty(value = "竞赛项目所取得的成果", required = true)
    @Excel(name = "竞赛项目所取得的成果", readConverterExp = "成=绩、奖励等")
    private String competitionAchievement;

    /** 竞赛项目现阶段存在的不足（专业知识、操作技能、临场心理、参赛条件等方面）与改进措施 */
    @ApiModelProperty(value = "竞赛项目现阶段存在的不足", required = true)
    @Excel(name = "竞赛项目现阶段存在的不足", readConverterExp = "专=业知识、操作技能、临场心理、参赛条件等方面")
    private String competitionInsufficient;

    /** 今后对竞赛项目的展望等 */
    @ApiModelProperty(value = "今后对竞赛项目的展望等", required = true)
    @Excel(name = "今后对竞赛项目的展望等")
    private String competitionNext;

    /** 研发人员劳务（校外） */
    @ApiModelProperty(value = "研发人员劳务", required = true)
    @Excel(name = "研发人员劳务")
    private Double cost0;

    /** 管理人员劳务（校外） */
    @ApiModelProperty(value = "管理人员劳务", required = true)
    @Excel(name = "管理人员劳务")
    private Double cost1;

    /** 耗材费 */
    @ApiModelProperty(value = "耗材费", required = true)
    @Excel(name = "耗材费")
    private Double cost2;

    /** 仪器设备使用费 */
    @ApiModelProperty(value = "仪器设备使用费", required = true)
    @Excel(name = "仪器设备使用费")
    private Double cost3;

    /** 专用设备购置 */
    @ApiModelProperty(value = "专用设备购置", required = true)
    @Excel(name = "专用设备购置")
    private Double cost4;

    /** 实验室改造 */
    @ApiModelProperty(value = "实验室改造", required = true)
    @Excel(name = "实验室改造")
    private Double cost5;

    /** 实验材料费 */
    @ApiModelProperty(value = "实验材料费", required = true)
    @Excel(name = "实验材料费")
    private Double cost6;

    /** 测试及试验费 */
    @ApiModelProperty(value = "测试及试验费", required = true)
    @Excel(name = "测试及试验费")
    private Double cost7;

    /** 学生参与活动的学生交通费 */
    @ApiModelProperty(value = "学生参与活动的学生交通费", required = true)
    @Excel(name = "学生参与活动的学生交通费")
    private Double cost8;

    /** 教师差旅费 */
    @ApiModelProperty(value = "教师差旅费", required = true)
    @Excel(name = "教师差旅费")
    private Double cost9;

    /** 非图书资料费 */
    @ApiModelProperty(value = "非图书资料费", required = true)
    @Excel(name = "非图书资料费")
    private Double cost10;

    /** 出版费、知识产权费 */
    @ApiModelProperty(value = "出版费、知识产权费", required = true)
    @Excel(name = "出版费、知识产权费")
    private Double cost11;

    /** 咨询、论证费 */
    @ApiModelProperty(value = "咨询、论证费", required = true)
    @Excel(name = "咨询、论证费")
    private Double cost12;

    /** 会议费（含培训费等） */
    @ApiModelProperty(value = "会议费", required = true)
    @Excel(name = "会议费", readConverterExp = "含=培训费等")
    private Double cost13;

    /** 图书 */
    @ApiModelProperty(value = "图书", required = true)
    @Excel(name = "图书")
    private Double cost14;

    /** 办公费 */
    @ApiModelProperty(value = "办公费", required = true)
    @Excel(name = "办公费")
    private Double cost15;

    /** 学生竞赛费用 */
    @ApiModelProperty(value = "学生竞赛费用", required = true)
    @Excel(name = "学生竞赛费用")
    private Double cost16;

    /** 报名考试（参赛证书）费 */
    @ApiModelProperty(value = "报名考试", required = true)
    @Excel(name = "报名考试", readConverterExp = "参=赛证书")
    private Double cost17;

    /** 其他费用1名称 */
    @ApiModelProperty(value = "其他费用1名称", required = true)
    @Excel(name = "其他费用1名称")
    private String cost18Name;

    /** 其他费用1金额 */
    @ApiModelProperty(value = "其他费用1金额", required = true)
    @Excel(name = "其他费用1金额")
    private Double cost18;

    /** 其他费用2名称 */
    @ApiModelProperty(value = "其他费用2名称", required = true)
    @Excel(name = "其他费用2名称")
    private String cost19Name;

    /** 其他费用2金额 */
    @ApiModelProperty(value = "其他费用2金额", required = true)
    @Excel(name = "其他费用2金额")
    private Double cost19;

    /** 其他费用3名称 */
    @ApiModelProperty(value = "其他费用3名称", required = true)
    @Excel(name = "其他费用3名称")
    private String cost20Name;

    /** 其他费用3金额 */
    @ApiModelProperty(value = "其他费用3金额", required = true)
    @Excel(name = "其他费用3金额")
    private Double cost20;

    /** 经费支出合计 */
    @ApiModelProperty(value = "经费支出合计", required = true)
    @Excel(name = "经费支出合计")
    private String cost21;

    /** 奖项设置最高级别 */
    @ApiModelProperty(value = "奖项设置最高级别", required = true)
    @Excel(name = "奖项设置最高级别")
    private String maxAward;

    /** 获奖项目名称 */
    @ApiModelProperty(value = "获奖项目名称", required = true)
    @Excel(name = "获奖项目名称")
    private String itemName;

    /** 获奖时间 */
    @ApiModelProperty(value = "获奖时间", required = true)
    @Excel(name = "获奖时间")
    private String awardTime;
    @Excel(name = "获奖时间")
    private String awardLevel;

}
