package com.ruoyi.web.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.domain.CompetitionBaseInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 竞赛信息基础信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Mapper
public interface CompetitionBaseInfoMapper extends BaseMapper<CompetitionBaseInfo>
{
    /**
     * 查询竞赛信息基础信息
     * 
     * @param id 竞赛信息基础信息主键
     * @return 竞赛信息基础信息
     */
    public CompetitionBaseInfo selectCompetitionBaseInfoById(Long id);

    /**
     * 查询竞赛信息基础信息列表
     * 
     * @param competitionBaseInfo 竞赛信息基础信息
     * @return 竞赛信息基础信息集合
     */
    public List<CompetitionBaseInfo> selectCompetitionBaseInfoList(CompetitionBaseInfo competitionBaseInfo);

    /**
     * 新增竞赛信息基础信息
     * 
     * @param competitionBaseInfo 竞赛信息基础信息
     * @return 结果
     */
    public int insertCompetitionBaseInfo(CompetitionBaseInfo competitionBaseInfo);

    /**
     * 修改竞赛信息基础信息
     * 
     * @param competitionBaseInfo 竞赛信息基础信息
     * @return 结果
     */
    public int updateCompetitionBaseInfo(CompetitionBaseInfo competitionBaseInfo);

    /**
     * 删除竞赛信息基础信息
     * 
     * @param id 竞赛信息基础信息主键
     * @return 结果
     */
    public int deleteCompetitionBaseInfoById(Long id);

    /**
     * 批量删除竞赛信息基础信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompetitionBaseInfoByIds(Long[] ids);
}
