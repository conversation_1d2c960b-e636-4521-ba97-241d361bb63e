<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.Project2scoreMapper">
    
    <resultMap type="Project2score" id="Project2scoreResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="applyName"    column="apply_name"    />
        <result property="projectTime"    column="project_time"    />
        <result property="amount"    column="amount"    />
        <result property="score"    column="score"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="isTeam"    column="is_team"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="nickName"    column="nick_name"    />
        <result property="categoryId"    column="category_id"    />
    </resultMap>

    <sql id="selectProject2scoreVo">
        select p.id, p.user_id, p.dept_id, p.rule_id, p.apply_name, p.project_time,
        p.amount, p.score, p.file_name, p.file_path, p.remark, p.status, p.is_team,
        p.create_by, p.create_time, p.update_by, p.update_time,
        u.nick_name,
        d.category_id
        from project2score p
        left join sys_user u on u.user_id = p.user_id
        left join project2score_rule_detail d on d.id = p.rule_id
    </sql>

    <select id="selectMyProject2scoreList" parameterType="Project2score" resultMap="Project2scoreResult">
        <include refid="selectProject2scoreVo"/>
        <where>
            <if test="userId != null "> and p.user_id = #{userId}</if>
            <if test="deptId != null "> and p.dept_id = #{deptId}</if>
            <if test="ruleId != null "> and p.rule_id = #{ruleId}</if>
            <if test="categoryId != null">
                and exists (
                    select 1 from project2score_rule_category c
                    where d.category_id = c.id 
                    and (
                        c.id = #{categoryId}
                        or find_in_set(#{categoryId}, c.ancestors)
                        or c.ancestors like concat(#{categoryId}, ',%')
                        or c.ancestors like concat('%,', #{categoryId}, ',%')
                        or c.ancestors like concat('%,', #{categoryId})
                    )
                )
            </if>
            <if test="applyName != null and applyName != ''"> and p.apply_name like concat('%', #{applyName}, '%')</if>
            <if test="params.beginProjectTime != null and params.beginProjectTime != '' and params.endProjectTime != null and params.endProjectTime != ''"> and p.project_time between #{params.beginProjectTime} and #{params.endProjectTime}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and p.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="status != null  and status != ''"> and p.status = #{status}</if>
            <if test="isTeam != null  and isTeam != ''"> and p.is_team = #{isTeam}</if>
        </where>
        order by 
            CASE p.status 
                WHEN '0' THEN 1  -- 草稿
                WHEN '3' THEN 2  -- 驳回
                WHEN '1' THEN 3  -- 待审核
                WHEN '2' THEN 4  -- 已通过
                ELSE 5
            END,
            p.create_time desc
    </select>

    <select id="selectProject2scoreList" parameterType="Project2score" resultMap="Project2scoreResult">
        <include refid="selectProject2scoreVo"/>
        <where>
            <if test="userId != null ">and p.user_id = #{userId}</if>
            <if test="deptId != null">
                AND (p.dept_id = #{deptId} OR p.dept_id IN ( 
                    SELECT t.dept_id FROM sys_dept t 
                    WHERE find_in_set(#{deptId}, ancestors) 
                ))
            </if>
            <if test="ruleId != null ">and p.rule_id = #{ruleId}</if>
            <if test="categoryId != null">
                and exists (
                    select 1 from project2score_rule_category c
                    where d.category_id = c.id 
                    and (
                        c.id = #{categoryId}
                        or find_in_set(#{categoryId}, c.ancestors)
                        or c.ancestors like concat(#{categoryId}, ',%')
                        or c.ancestors like concat('%,', #{categoryId}, ',%')
                        or c.ancestors like concat('%,', #{categoryId})
                    )
                )
            </if>
            <if test="applyName != null  and applyName != ''">and p.apply_name like concat('%', #{applyName}, '%')</if>
            <if test="projectTime != null ">and p.project_time = #{projectTime}</if>
            <if test="status != null  and status != ''">and p.status = #{status}</if>
            <if test="isTeam != null  and isTeam != ''">and p.is_team = #{isTeam}</if>
            <if test="params.beginProjectTime != null and params.beginProjectTime != ''">
                and date_format(p.project_time,'%y%m%d') &gt;= date_format(#{params.beginProjectTime},'%y%m%d')
            </if>
            <if test="params.endProjectTime != null and params.endProjectTime != ''">
                and date_format(p.project_time,'%y%m%d') &lt;= date_format(#{params.endProjectTime},'%y%m%d')
            </if>
        </where>
        order by p.create_time desc
    </select>
    
    <select id="selectProject2scoreById" parameterType="Long" resultMap="Project2scoreResult">
        <include refid="selectProject2scoreVo"/>
        where p.id = #{id}
    </select>
        
    <insert id="insertProject2score" parameterType="Project2score" useGeneratedKeys="true" keyProperty="id">
        insert into project2score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="applyName != null and applyName != ''">apply_name,</if>
            <if test="projectTime != null">project_time,</if>
            <if test="amount != null">amount,</if>
            <if test="score != null">score,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="isTeam != null and isTeam != ''">is_team,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="applyName != null and applyName != ''">#{applyName},</if>
            <if test="projectTime != null">#{projectTime},</if>
            <if test="amount != null">#{amount},</if>
            <if test="score != null">#{score},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="isTeam != null and isTeam != ''">#{isTeam},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProject2score" parameterType="Project2score">
        update project2score
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="applyName != null and applyName != ''">apply_name = #{applyName},</if>
            <if test="projectTime != null">project_time = #{projectTime},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="score != null">score = #{score},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="isTeam != null and isTeam != ''">is_team = #{isTeam},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProject2scoreById" parameterType="Long">
        delete from project2score where id = #{id}
    </delete>

    <delete id="deleteProject2scoreByIds" parameterType="String">
        delete from project2score where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectProject2scoreAuditList" parameterType="Project2score" resultMap="Project2scoreResult">
        <include refid="selectProject2scoreVo"/>
        <where>  
            <if test="userId != null "> and p.user_id = #{userId}</if>
            <if test="nickName != null and nickName != ''"> 
                and (u.nick_name like concat('%', #{nickName}, '%') or u.user_name like concat('%', #{nickName}, '%'))
            </if>
            <if test="deptId != null">
                and exists (
                    select 1 from sys_dept d 
                    where (d.dept_id = p.dept_id 
                        and (d.dept_id = #{deptId} or find_in_set(#{deptId}, d.ancestors))
                    )
                )
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                <!-- 基于ancestors实现的部门权限过滤，过滤一级部门及其子部门数据 -->
                and p.dept_id in
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="ruleId != null "> and p.rule_id = #{ruleId}</if>
            <if test="params.beginProjectTime != null and params.beginProjectTime != '' and params.endProjectTime != null and params.endProjectTime != ''"> and p.project_time between #{params.beginProjectTime} and #{params.endProjectTime}</if>
            <if test="status != null  and status != ''"> and p.status = #{status}</if>
            <if test="isTeam != null  and isTeam != ''"> and p.is_team = #{isTeam}</if>
        </where>
        order by 
            CASE p.status 
                WHEN '1' THEN 1  -- 待审核
                WHEN '3' THEN 2  -- 已驳回
                WHEN '2' THEN 3  -- 已通过
                WHEN '0' THEN 4  -- 草稿
                ELSE 5
            END,
            p.create_time desc
    </select>

    <!-- 导出数据查询 -->
    <select id="selectProject2scoreExportList" parameterType="Project2score" resultType="com.ruoyi.web.domain.vo.Project2scoreExportVO">
        SELECT 
            p.apply_name,
            CONCAT(c.category_name, ' / ', d.rule_name) as rule_name,
            p.project_time,
            p.score,
            p.is_team,
            CASE 
                WHEN p.is_team = '1' THEN 
                    GROUP_CONCAT(
                        CONCAT(
                            CASE pd.member_type 
                                WHEN '0' THEN COALESCE(u.nick_name, pd.member_name)
                                ELSE pd.member_name 
                            END,
                            '(', pd.ratio, '%, ', pd.score, '分)'
                        ) 
                        ORDER BY pd.is_leader DESC, pd.id ASC 
                        SEPARATOR '; '
                    )
                ELSE NULL
            END as team_member_scores,
            p.status
        FROM project2score p
        LEFT JOIN project2score_rule_detail d ON p.rule_id = d.id
        LEFT JOIN project2score_rule_category c ON d.category_id = c.id
        LEFT JOIN project2score_distribution pd ON p.id = pd.project_id
        LEFT JOIN sys_user u ON pd.user_id = u.user_id
        <where>
            <if test="userId != null "> and p.user_id = #{userId}</if>
            <if test="nickName != null and nickName != ''"> 
                and (u.nick_name like concat('%', #{nickName}, '%') or u.user_name like concat('%', #{nickName}, '%'))
            </if>
            <if test="deptId != null">
                and exists (
                    select 1 from sys_dept d 
                    where (d.dept_id = p.dept_id 
                        and (d.dept_id = #{deptId} or find_in_set(#{deptId}, d.ancestors))
                    )
                )
            </if>
            <if test="ruleId != null "> and p.rule_id = #{ruleId}</if>
            <if test="categoryId != null">
                and exists (
                    select 1 from project2score_rule_category rc
                    where d.category_id = rc.id 
                    and (
                        rc.id = #{categoryId}
                        or find_in_set(#{categoryId}, rc.ancestors)
                        or rc.ancestors like concat(#{categoryId}, ',%')
                        or rc.ancestors like concat('%,', #{categoryId}, ',%')
                        or rc.ancestors like concat('%,', #{categoryId})
                    )
                )
            </if>
            <if test="applyName != null and applyName != ''"> and p.apply_name like concat('%', #{applyName}, '%')</if>
            <if test="params.beginProjectTime != null and params.beginProjectTime != '' and params.endProjectTime != null and params.endProjectTime != ''"> and p.project_time between #{params.beginProjectTime} and #{params.endProjectTime}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and p.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="status != null  and status != ''"> and p.status = #{status}</if>
            <if test="isTeam != null  and isTeam != ''"> and p.is_team = #{isTeam}</if>
        </where>
        GROUP BY p.id
        ORDER BY p.create_time DESC
    </select>

    <!-- 审核记录导出查询 -->
    <select id="selectProject2scoreAuditExportList" parameterType="Project2score" resultType="com.ruoyi.web.domain.vo.Project2scoreAuditExportVO">
        SELECT 
            u1.nick_name,
            p.apply_name,
            CONCAT(c.category_name, ' / ', d.rule_name) as rule_name,
            p.project_time,
            p.score,
            p.is_team,
            CASE 
                WHEN p.is_team = '1' THEN 
                    GROUP_CONCAT(
                        CONCAT(
                            CASE pd.member_type 
                                WHEN '0' THEN COALESCE(u2.nick_name, pd.member_name)
                                ELSE pd.member_name 
                            END,
                            '(', pd.ratio, '%, ', pd.score, '分)'
                        ) 
                        ORDER BY pd.is_leader DESC, pd.id ASC 
                        SEPARATOR '; '
                    )
                ELSE NULL
            END as team_member_scores,
            p.status,
            u3.nick_name as audit_user_name,
            ar.audit_status,
            ar.audit_remark,
            ar.create_time as audit_time
        FROM project2score p
        LEFT JOIN sys_user u1 ON p.user_id = u1.user_id
        LEFT JOIN project2score_rule_detail d ON p.rule_id = d.id
        LEFT JOIN project2score_rule_category c ON d.category_id = c.id
        LEFT JOIN project2score_distribution pd ON p.id = pd.project_id
        LEFT JOIN sys_user u2 ON pd.user_id = u2.user_id
        LEFT JOIN (
            SELECT t1.*
            FROM project2score_audit_record t1
            WHERE NOT EXISTS (
                SELECT 1 FROM project2score_audit_record t2
                WHERE t1.project_id = t2.project_id 
                AND t1.create_time &lt; t2.create_time
            )
        ) ar ON p.id = ar.project_id
        LEFT JOIN sys_user u3 ON ar.audit_user_id = u3.user_id
        <where>
            <if test="userId != null "> and p.user_id = #{userId}</if>
            <if test="nickName != null and nickName != ''"> 
                and (u1.nick_name like concat('%', #{nickName}, '%') or u1.user_name like concat('%', #{nickName}, '%'))
            </if>
            <if test="deptId != null">
                and exists (
                    select 1 from sys_dept d 
                    where (d.dept_id = p.dept_id 
                        and (d.dept_id = #{deptId} or find_in_set(#{deptId}, d.ancestors))
                    )
                )
            </if>
            <if test="ruleId != null "> and p.rule_id = #{ruleId}</if>
            <if test="categoryId != null">
                and exists (
                    select 1 from project2score_rule_category rc
                    where d.category_id = rc.id 
                    and (
                        rc.id = #{categoryId}
                        or find_in_set(#{categoryId}, rc.ancestors)
                        or rc.ancestors like concat(#{categoryId}, ',%')
                        or rc.ancestors like concat('%,', #{categoryId}, ',%')
                        or rc.ancestors like concat('%,', #{categoryId})
                    )
                )
            </if>
            <if test="applyName != null and applyName != ''"> and p.apply_name like concat('%', #{applyName}, '%')</if>
            <if test="params.beginProjectTime != null and params.beginProjectTime != ''">
                and date_format(p.project_time,'%y%m%d') &gt;= date_format(#{params.beginProjectTime},'%y%m%d')
            </if>
            <if test="params.endProjectTime != null and params.endProjectTime != ''">
                and date_format(p.project_time,'%y%m%d') &lt;= date_format(#{params.endProjectTime},'%y%m%d')
            </if>
            <if test="status != null  and status != ''"> and p.status = #{status}</if>
            <if test="isTeam != null  and isTeam != ''"> and p.is_team = #{isTeam}</if>
        </where>
        GROUP BY p.id, ar.audit_user_id, ar.audit_status, ar.audit_remark, ar.create_time
        ORDER BY p.create_time DESC
    </select>
</mapper>