<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.Project2scoreDistributionMapper">
    
    <resultMap type="Project2scoreDistribution" id="Project2scoreDistributionResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="ratio"    column="ratio"    />
        <result property="score"    column="score"    />
        <result property="isLeader"    column="is_leader"    />
        <result property="memberType"    column="member_type"    />
        <result property="memberName"    column="member_name"    />
        <result property="memberIdentity"    column="member_identity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProject2scoreDistributionVo">
        SELECT 
            d.id, d.project_id, d.user_id, d.dept_id, d.ratio, d.score, 
            d.is_leader, d.member_type, d.member_name, d.member_identity, 
            d.create_by, d.create_time, d.update_by, d.update_time,
            dept.dept_name
        FROM project2score_distribution d
        LEFT JOIN sys_dept dept ON d.dept_id = dept.dept_id
    </sql>

    <select id="selectProject2scoreDistributionList" parameterType="Project2scoreDistribution" resultMap="Project2scoreDistributionResult">
        SELECT 
            d.id, d.project_id, d.user_id, d.dept_id, d.ratio, d.score, 
            d.is_leader, d.member_type, d.member_name, d.member_identity, 
            d.create_by, d.create_time, d.update_by, d.update_time,
            dept.dept_name
        FROM project2score_distribution d
        LEFT JOIN sys_dept dept ON d.dept_id = dept.dept_id
        <where>  
            <if test="projectId != null "> and d.project_id = #{projectId}</if>
            <if test="userId != null "> and d.user_id = #{userId}</if>
            <if test="deptId != null "> and d.dept_id = #{deptId}</if>
            <if test="ratio != null "> and d.ratio = #{ratio}</if>
            <if test="score != null "> and d.score = #{score}</if>
            <if test="isLeader != null  and isLeader != ''"> and d.is_leader = #{isLeader}</if>
            <if test="memberType != null  and memberType != ''"> and d.member_type = #{memberType}</if>
            <if test="memberName != null  and memberName != ''"> and d.member_name like concat('%', #{memberName}, '%')</if>
            <if test="memberIdentity != null  and memberIdentity != ''"> and d.member_identity like concat('%', #{memberIdentity}, '%')</if>
        </where>
    </select>
    
    <select id="selectProject2scoreDistributionById" parameterType="Long" resultMap="Project2scoreDistributionResult">
        SELECT 
            d.id, d.project_id, d.user_id, d.dept_id, d.ratio, d.score, 
            d.is_leader, d.member_type, d.member_name, d.member_identity, 
            d.create_by, d.create_time, d.update_by, d.update_time,
            dept.dept_name
        FROM project2score_distribution d
        LEFT JOIN sys_dept dept ON d.dept_id = dept.dept_id
        WHERE d.id = #{id}
    </select>
        
    <insert id="insertProject2scoreDistribution" parameterType="Project2scoreDistribution" useGeneratedKeys="true" keyProperty="id">
        insert into project2score_distribution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="ratio != null">ratio,</if>
            <if test="score != null">score,</if>
            <if test="isLeader != null and isLeader != ''">is_leader,</if>
            <if test="memberType != null and memberType != ''">member_type,</if>
            <if test="memberName != null and memberName != ''">member_name,</if>
            <if test="memberIdentity != null and memberIdentity != ''">member_identity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="ratio != null">#{ratio},</if>
            <if test="score != null">#{score},</if>
            <if test="isLeader != null and isLeader != ''">#{isLeader},</if>
            <if test="memberType != null and memberType != ''">#{memberType},</if>
            <if test="memberName != null and memberName != ''">#{memberName},</if>
            <if test="memberIdentity != null and memberIdentity != ''">#{memberIdentity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProject2scoreDistribution" parameterType="Project2scoreDistribution">
        update project2score_distribution
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="ratio != null">ratio = #{ratio},</if>
            <if test="score != null">score = #{score},</if>
            <if test="isLeader != null and isLeader != ''">is_leader = #{isLeader},</if>
            <if test="memberType != null and memberType != ''">member_type = #{memberType},</if>
            <if test="memberName != null and memberName != ''">member_name = #{memberName},</if>
            <if test="memberIdentity != null and memberIdentity != ''">member_identity = #{memberIdentity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProject2scoreDistributionById" parameterType="Long">
        delete from project2score_distribution where id = #{id}
    </delete>

    <delete id="deleteProject2scoreDistributionByIds" parameterType="String">
        delete from project2score_distribution where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 删除项目的所有团队成员 -->
    <delete id="deleteByProjectId" parameterType="Long">
        delete from project2score_distribution where project_id = #{projectId}
    </delete>

    <!-- 批量新增团队成员分配 -->
    <insert id="batchInsertDistributions" parameterType="java.util.List">
        insert into project2score_distribution (
            project_id, user_id, dept_id, ratio, score, is_leader, member_type, member_name, member_identity,
            create_by, create_time, update_by, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.projectId},
            #{item.userId},
            #{item.deptId},
            #{item.ratio},
            #{item.score},
            #{item.isLeader},
            #{item.memberType},
            #{item.memberName},
            #{item.memberIdentity},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 查询当前用户参与的团队项目积分分配列表 -->
    <resultMap type="com.ruoyi.web.domain.vo.Project2scoreDistributionVO" id="Project2scoreDistributionVOResult" extends="Project2scoreDistributionResult">
        <result property="projectName" column="apply_name" />
        <result property="status" column="status" />
        <result property="projectTime" column="project_time" />
        <result property="ruleId" column="rule_id" />
        <result property="userName" column="user_name" />
        <result property="deptName" column="dept_name" />
    </resultMap>

    <select id="selectMyDistributionList" parameterType="com.ruoyi.web.domain.vo.Project2scoreDistributionVO" resultMap="Project2scoreDistributionVOResult">
        select d.id, d.project_id, d.user_id, d.ratio, d.score, d.is_leader,
               d.member_type, d.member_name, d.member_identity,
               p.apply_name, p.status, p.project_time, p.rule_id,
               u.nick_name as user_name, d2.dept_name
        from project2score_distribution d
        left join project2score p on d.project_id = p.id
        left join sys_user u on d.user_id = u.user_id
        left join sys_dept d2 on u.dept_id = d2.dept_id
        <where>
            <if test="userId != null">
                AND d.user_id = #{userId}
            </if>
            <if test="projectId != null">
                AND d.project_id = #{projectId}
            </if>
            <if test="projectName != null and projectName != ''">
                AND p.apply_name like concat('%', #{projectName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND p.status = #{status}
            </if>
            <if test="projectTimeStart != null">
                AND p.project_time &gt;= #{projectTimeStart}
            </if>
            <if test="projectTimeEnd != null">
                AND p.project_time &lt;= #{projectTimeEnd}
            </if>
            <if test="isLeader != null and isLeader != ''">
                AND d.is_leader = #{isLeader}
            </if>
            AND p.is_team = '1'
            AND d.user_id != 0
        </where>
        order by p.create_time desc
    </select>

    <!-- 根据项目ID查询团队成员分配记录 -->
    <select id="selectProject2scoreDistributionByProjectId" parameterType="Long" resultMap="Project2scoreDistributionResult">
        SELECT 
            d.id, d.project_id, d.user_id, d.dept_id, d.ratio, d.score, 
            d.is_leader, d.member_type, d.member_name, d.member_identity,
            d.create_by, d.create_time, d.update_by, d.update_time,
            dept.dept_name
        FROM project2score_distribution d
        LEFT JOIN sys_dept dept ON d.dept_id = dept.dept_id
        WHERE d.project_id = #{projectId}
        ORDER BY d.is_leader DESC, d.create_time ASC
    </select>

    <!-- 批量新增团队成员分配记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        insert into project2score_distribution (
            project_id, user_id, ratio, score, is_leader, member_type, member_name, member_identity,
            create_by, create_time, update_by, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.projectId}, #{item.userId}, #{item.ratio}, #{item.score}, #{item.isLeader},
                #{item.memberType}, #{item.memberName}, #{item.memberIdentity},
                #{item.createBy}, sysdate(), #{item.updateBy}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 根据项目ID删除团队成员分配记录 -->
    <delete id="deleteByProjectId" parameterType="Long">
        delete from project2score_distribution where project_id = #{projectId}
    </delete>

    <!-- 导出数据查询 -->
    <select id="selectDistributionExportList" parameterType="com.ruoyi.web.domain.vo.Project2scoreDistributionVO" resultType="com.ruoyi.web.domain.vo.Project2scoreDistributionExportVO">
        SELECT 
            p.id as projectId,
            p.apply_name as projectName,
            p.project_time as projectTime,
            (
                SELECT dept.dept_name 
                FROM project2score_distribution dist
                LEFT JOIN sys_user usr ON dist.user_id = usr.user_id
                LEFT JOIN sys_dept dept ON usr.dept_id = dept.dept_id
                WHERE dist.project_id = p.id 
                AND dist.is_leader = '1'
                LIMIT 1
            ) as deptName,
            p.score as totalScore,
            p.status,
            (
                SELECT dist.is_leader
                FROM project2score_distribution dist
                WHERE dist.project_id = p.id 
                AND dist.user_id = #{userId}
                LIMIT 1
            ) as isLeader,
            (
                SELECT GROUP_CONCAT(
                    CASE dist.member_type 
                        WHEN '0' THEN COALESCE(usr.nick_name, dist.member_name)
                        ELSE dist.member_name 
                    END
                    ORDER BY dist.is_leader DESC, dist.id ASC 
                    SEPARATOR ', '
                )
                FROM project2score_distribution dist
                LEFT JOIN sys_user usr ON dist.user_id = usr.user_id
                WHERE dist.project_id = p.id
            ) as teamMembers,
            (
                SELECT GROUP_CONCAT(
                    CONCAT(
                        CASE dist.member_type 
                            WHEN '0' THEN COALESCE(usr.nick_name, dist.member_name)
                            ELSE dist.member_name 
                        END,
                        CASE dist.is_leader 
                            WHEN '1' THEN '(负责人, '
                            ELSE '(' 
                        END,
                        dist.ratio, '%, ', dist.score, '分)'
                    ) 
                    ORDER BY dist.is_leader DESC, dist.id ASC 
                    SEPARATOR '; '
                )
                FROM project2score_distribution dist
                LEFT JOIN sys_user usr ON dist.user_id = usr.user_id
                WHERE dist.project_id = p.id
            ) as scoreDistribution
        FROM project2score p
        WHERE p.is_team = '1'
        <if test="userId != null">
            AND EXISTS (
                SELECT 1 FROM project2score_distribution d3 
                WHERE d3.project_id = p.id 
                AND d3.user_id = #{userId}
            )
        </if>
        <if test="projectId != null">
            AND p.id = #{projectId}
        </if>
        <if test="projectName != null and projectName != ''">
            AND p.apply_name like concat('%', #{projectName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND p.status = #{status}
        </if>
        <if test="projectTimeStart != null">
            AND p.project_time &gt;= #{projectTimeStart}
        </if>
        <if test="projectTimeEnd != null">
            AND p.project_time &lt;= #{projectTimeEnd}
        </if>
        ORDER BY p.project_time DESC, p.id DESC
    </select>
</mapper>