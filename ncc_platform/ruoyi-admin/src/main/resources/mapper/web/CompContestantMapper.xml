<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.CompContestantMapper">
    
    <resultMap type="CompContestant" id="CompContestantResult">
        <result property="id"    column="id"    />
        <result property="competitionId"    column="competition_id"    />
        <result property="contestantRole"    column="contestant_role"    />
        <result property="contestantId"    column="contestant_id"    />
        <result property="awards"    column="awards"    />
        <result property="type"    column="type"    />
        <result property="major"    column="major"    />
        <result property="no"    column="no"    />
        <result property="group"    column="group"    />
    </resultMap>

    <resultMap type="CompContestantVO" id="CompContestantResultVO">
        <result property="id"    column="id"    />
        <result property="contestantRole"    column="contestant_role"    />
        <result property="competitionId"    column="competition_id"    />
        <result property="contestantId"    column="contestant_id"    />
        <result property="awards"    column="awards"    />
        <result property="type"    column="type"    />
        <result property="major"    column="major"    />
        <result property="no"    column="no"    />
        <result property="group"    column="group"    />
    </resultMap>

    <sql id="selectCompContestantVo">
        select id, competition_id, contestant_id,contestant_role, awards, type ,major, `no`, `group` from comp_contestant
    </sql>

    <select id="selectCompContestantList" parameterType="CompContestant" resultMap="CompContestantResult">
        <include refid="selectCompContestantVo"/>
        <where>  
            <if test="competitionId != null "> and competition_id = #{competitionId}</if>
            <if test="contestantId != null  and contestantId != ''"> and contestant_id = #{contestantId}</if>
            <if test="contestantRole != null  and contestantRole != ''"> and `contestant_role` like concat('%', #{contestantRole}, '%')</if>
            <if test="awards != null  and awards != ''"> and awards = #{awards}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="major != null "> and major = #{major}</if>
            <if test="no != null  and no != ''"> and `no` = #{no}</if>
            <if test="group != null  and group != ''"> and `group` = #{group}</if>
        </where>
    </select>
    
    <select id="selectCompContestantById" parameterType="Long" resultMap="CompContestantResult">
        <include refid="selectCompContestantVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCompContestant" parameterType="CompContestant" useGeneratedKeys="true" keyProperty="id">
        insert into comp_contestant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="competitionId != null">competition_id,</if>
            <if test="contestantRole != null">contestant_role,</if>
            <if test="contestantId != null">contestant_id,</if>
            <if test="awards != null">awards,</if>
            <if test="type != null">type,</if>
            <if test="major != null">major,</if>
            <if test="no != null">no,</if>
            <if test="group != null">`group`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="competitionId != null">#{competitionId},</if>
            <if test="contestantRole != null">#{contestantRole},</if>
            <if test="contestantId != null">#{contestantId},</if>
            <if test="awards != null">#{awards},</if>
            <if test="type != null">#{type},</if>
            <if test="major != null">#{major},</if>
            <if test="no != null">#{no},</if>
            <if test="group != null">#{group},</if>
         </trim>
    </insert>

    <update id="updateCompContestant" parameterType="CompContestant">
        update comp_contestant
        <trim prefix="SET" suffixOverrides=",">
            <if test="competitionId != null">competition_id = #{competitionId},</if>
            <if test="contestantRole != null">contestant_role = #{contestantRole},</if>
            <if test="contestantId != null">contestant_id = #{contestantId},</if>
            <if test="awards != null">awards = #{awards},</if>
            <if test="type != null">type = #{type},</if>
            <if test="major != null">major = #{major},</if>
            <if test="no != null">no = #{no},</if>
            <if test="group != null">`group` = #{group},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCompContestantById" parameterType="Long">
        delete from comp_contestant where id = #{id}
    </delete>

    <delete id="deleteCompContestantByIds" parameterType="String">
        delete from comp_contestant where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCompContestantListPlus" parameterType="CompContestant" resultMap="CompContestantResultVO">
        select cc.id, cc.competition_id,cc.contestant_role, cc.contestant_id, cc.awards, cc.`type` , baseInfo.`name` as competition_name, cc.major, cc.`no`, cc.`group` from comp_contestant cc
        LEFT JOIN competition_base_info baseInfo ON cc.competition_id = baseInfo.id
        <where>
            <if test="competitionId != null "> and cc.competition_id = #{competitionId}</if>
            <if test="contestantId != null  and contestantId != ''"> and cc.contestant_id = #{contestantId}</if>
            <if test="awards != null  and awards != ''"> and cc.awards = #{awards}</if>
            <if test="type != null "> and cc.type = #{type}</if>
            <if test="major != null "> and cc.major = #{major}</if>
            <if test="no != null  and no != ''"> and cc.`no` = #{no}</if>
        </where>
        order by cc.`group`
    </select>
</mapper>