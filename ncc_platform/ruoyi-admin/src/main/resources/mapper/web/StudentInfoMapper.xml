<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.StudentInfoMapper">
    
    <resultMap type="StudentInfo" id="StudentInfoResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="major"    column="major"    />
        <result property="className"    column="class_name"    />
        <result property="no"    column="no"    />
        <result property="gender"    column="gender"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStudentInfoVo">
        select id, name, major, class_name, no, gender, create_time, update_time from student_info
    </sql>

    <select id="selectStudentInfoList" parameterType="StudentInfo" resultMap="StudentInfoResult">
        <include refid="selectStudentInfoVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="major != null  and major != ''"> and major = #{major}</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="no != null  and no != ''"> and no like concat('%', #{no}, '%')</if>
            <if test="gender != null "> and gender = #{gender}</if>
        </where>
    </select>
    
    <select id="selectStudentInfoById" parameterType="Long" resultMap="StudentInfoResult">
        <include refid="selectStudentInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertStudentInfo" parameterType="StudentInfo" useGeneratedKeys="true" keyProperty="id">
        insert into student_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="major != null and major != ''">major,</if>
            <if test="className != null and className != ''">class_name,</if>
            <if test="no != null and no != ''">no,</if>
            <if test="gender != null">gender,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="major != null and major != ''">#{major},</if>
            <if test="className != null and className != ''">#{className},</if>
            <if test="no != null and no != ''">#{no},</if>
            <if test="gender != null">#{gender},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateStudentInfo" parameterType="StudentInfo">
        update student_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="major != null and major != ''">major = #{major},</if>
            <if test="className != null and className != ''">class_name = #{className},</if>
            <if test="no != null and no != ''">no = #{no},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStudentInfoById" parameterType="Long">
        delete from student_info where id = #{id}
    </delete>

    <delete id="deleteStudentInfoByIds" parameterType="String">
        delete from student_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>