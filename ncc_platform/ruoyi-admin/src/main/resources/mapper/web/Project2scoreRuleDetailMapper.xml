<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.Project2scoreRuleDetailMapper">
    
    <resultMap type="Project2scoreRuleDetail" id="Project2scoreRuleDetailResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="projectTimeName"    column="project_time_name"    />
        <result property="applyNameLabel"    column="apply_name_label"    />
        <result property="ruleSort"    column="rule_sort"    />
        <result property="scoreValue"    column="score_value"    />
        <result property="isAmount"    column="is_amount"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProject2scoreRuleDetailVo">
        select id, category_id, rule_name, project_time_name, apply_name_label, rule_sort, score_value, is_amount, status, remark, create_by, create_time, update_by, update_time from project2score_rule_detail
    </sql>

    <select id="selectProject2scoreRuleDetailList" parameterType="Project2scoreRuleDetail" resultMap="Project2scoreRuleDetailResult">
        <include refid="selectProject2scoreRuleDetailVo"/>
        <where>  
            <if test="categoryId != null ">
                and exists (
                    select 1 from project2score_rule_category rc
                    where category_id = rc.id 
                    and (
                        rc.id = #{categoryId}
                        or rc.ancestors like concat('%,', #{categoryId}, ',%')
                        or rc.ancestors like concat('%,', #{categoryId})
                    )
                )
            </if>
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="isAmount != null  and isAmount != ''"> and is_amount = #{isAmount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by rule_sort asc, create_time desc
    </select>
    
    <select id="selectProject2scoreRuleDetailById" parameterType="Long" resultMap="Project2scoreRuleDetailResult">
        <include refid="selectProject2scoreRuleDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertProject2scoreRuleDetail" parameterType="Project2scoreRuleDetail" useGeneratedKeys="true" keyProperty="id">
        insert into project2score_rule_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="projectTimeName != null and projectTimeName != ''">project_time_name,</if>
            <if test="applyNameLabel != null and applyNameLabel != ''">apply_name_label,</if>
            <if test="ruleSort != null">rule_sort,</if>
            <if test="scoreValue != null">score_value,</if>
            <if test="isAmount != null and isAmount != ''">is_amount,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="projectTimeName != null and projectTimeName != ''">#{projectTimeName},</if>
            <if test="applyNameLabel != null and applyNameLabel != ''">#{applyNameLabel},</if>
            <if test="ruleSort != null">#{ruleSort},</if>
            <if test="scoreValue != null">#{scoreValue},</if>
            <if test="isAmount != null and isAmount != ''">#{isAmount},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProject2scoreRuleDetail" parameterType="Project2scoreRuleDetail">
        update project2score_rule_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="projectTimeName != null and projectTimeName != ''">project_time_name = #{projectTimeName},</if>
            <if test="applyNameLabel != null and applyNameLabel != ''">apply_name_label = #{applyNameLabel},</if>
            <if test="ruleSort != null">rule_sort = #{ruleSort},</if>
            <if test="scoreValue != null">score_value = #{scoreValue},</if>
            <if test="isAmount != null and isAmount != ''">is_amount = #{isAmount},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProject2scoreRuleDetailById" parameterType="Long">
        delete from project2score_rule_detail where id = #{id}
    </delete>

    <delete id="deleteProject2scoreRuleDetailByIds" parameterType="String">
        delete from project2score_rule_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateStatusByCategoryId">
        update project2score_rule_detail
        set status = #{status},
            update_time = sysdate()
        where category_id = #{categoryId}
    </update>
</mapper>