<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.Project2scoreRecordMapper">
    
    <resultMap type="Project2scoreRecord" id="Project2scoreRecordResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="score"    column="score"    />
        <result property="type"    column="type"    />
        <result property="remark"    column="remark"    />
        <result property="projectTime"    column="project_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="nickName"    column="nick_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="projectName"    column="project_name"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="isTeam"    column="is_team"    />
        <result property="isLeader"    column="is_leader"    />
    </resultMap>

    <resultMap type="com.ruoyi.web.domain.vo.Project2scoreRecordExportVO" id="ExportResultMap">
        <result property="nickName" column="nick_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="projectName" column="project_name"/>
        <result property="projectTime" column="project_time"/>
        <result property="isTeam" column="is_team"/>
        <result property="isLeader" column="is_leader"/>
        <result property="ruleName" column="rule_name"/>
        <result property="score" column="score"/>
    </resultMap>

    <sql id="selectProject2scoreRecordVo">
        select r.id, 
               r.user_id, 
               r.dept_id, 
               r.project_id, 
               r.rule_id, 
               r.score, 
               r.type, 
               r.remark, 
               r.project_time, 
               r.create_time,
               u.nick_name,
               d.dept_name,
               p.apply_name as project_name,
               rd.rule_name,
               p.is_team,
               CASE 
                   WHEN p.is_team = '1' THEN pd.is_leader 
                   ELSE '0' 
               END as is_leader
        from project2score_record r
        left join sys_user u on u.user_id = r.user_id
        left join sys_dept d on d.dept_id = r.dept_id
        left join project2score p on p.id = r.project_id
        left join project2score_rule_detail rd on rd.id = r.rule_id
        left join project2score_distribution pd on (p.id = pd.project_id and pd.user_id = r.user_id)
    </sql>

    <select id="selectProject2scoreRecordList" parameterType="Project2scoreRecord" resultMap="Project2scoreRecordResult">
        <include refid="selectProject2scoreRecordVo"/>
        <where>  
            <if test="userId != null ">and r.user_id = #{userId}</if>
            <if test="nickName != null and nickName != ''"> 
                and (u.nick_name like concat('%', #{nickName}, '%') or u.user_name like concat('%', #{nickName}, '%'))
            </if>
            <if test="deptId != null">
                and exists (
                    select 1 from sys_dept d2 
                    where (d2.dept_id = r.dept_id 
                        and (d2.dept_id = #{deptId} or find_in_set(#{deptId}, d2.ancestors))
                    )
                )
            </if>
            <if test="projectId != null ">and r.project_id = #{projectId}</if>
            <if test="ruleId != null or categoryId != null">
                and (
                    <if test="ruleId != null">
                        r.rule_id = #{ruleId}
                    </if>
                    <if test="ruleId != null and categoryId != null">
                        or
                    </if>
                    <if test="categoryId != null">
                        exists (
                            select 1 
                            from project2score_rule_detail detail
                            join project2score_rule_category rc on detail.category_id = rc.id
                            where detail.id = r.rule_id
                            and (
                                rc.id = #{categoryId}
                                or find_in_set(#{categoryId}, rc.ancestors)
                                or rc.ancestors like concat(#{categoryId}, ',%')
                                or rc.ancestors like concat('%,', #{categoryId}, ',%')
                                or rc.ancestors like concat('%,', #{categoryId})
                            )
                        )
                    </if>
                )
            </if>
            <if test="score != null ">and r.score = #{score}</if>
            <if test="type != null  and type != ''">and r.type = #{type}</if>
            <if test="params.beginProjectTime != null and params.beginProjectTime != ''"><!-- 开始项目时间 -->
                and r.project_time &gt;= #{params.beginProjectTime}
            </if>
            <if test="params.endProjectTime != null and params.endProjectTime != ''"><!-- 结束项目时间 -->
                and r.project_time &lt;= #{params.endProjectTime}
            </if>
        </where>
        order by r.create_time desc
    </select>
    
    <select id="selectProject2scoreRecordById" parameterType="Long" resultMap="Project2scoreRecordResult">
        <include refid="selectProject2scoreRecordVo"/>
        where r.id = #{id}
    </select>
        
    <insert id="insertProject2scoreRecord" parameterType="Project2scoreRecord" useGeneratedKeys="true" keyProperty="id">
        insert into project2score_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="score != null">score,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="remark != null">remark,</if>
            <if test="projectTime != null">project_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="score != null">#{score},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="remark != null">#{remark},</if>
            <if test="projectTime != null">#{projectTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateProject2scoreRecord" parameterType="Project2scoreRecord">
        update project2score_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="score != null">score = #{score},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="projectTime != null">project_time = #{projectTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProject2scoreRecordById" parameterType="Long">
        delete from project2score_record where id = #{id}
    </delete>

    <delete id="deleteProject2scoreRecordByIds" parameterType="String">
        delete from project2score_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteProject2scoreRecordByProjectId" parameterType="Long">
        delete from project2score_record where project_id = #{projectId}
    </delete>

    <!-- 关联查询积分记录列表 -->
    <select id="selectProject2scoreRecordVOList" parameterType="Project2scoreRecord" resultType="com.ruoyi.web.domain.vo.Project2scoreRecordVO">
        select r.id,
               r.user_id,
               u.nick_name,
               r.dept_id,
               d.dept_name,
               r.project_id,
               p.apply_name as project_name,
               r.rule_id,
               rd.rule_name,
               r.score,
               r.type,
               r.remark,
               r.project_time,
               r.create_time
        from project2score_record r
        left join sys_user u on u.user_id = r.user_id
        left join sys_dept d on d.dept_id = r.dept_id
        left join project2score p on p.id = r.project_id
        left join project2score_rule_detail rd on rd.id = r.rule_id
        left join project2score_rule_category c on rd.category_id = c.id
        <where>
            <if test="userId != null ">and r.user_id = #{userId}</if>
            <if test="nickName != null and nickName != ''"> 
                and (u.nick_name like concat('%', #{nickName}, '%') or u.user_name like concat('%', #{nickName}, '%'))
            </if>
            <if test="deptId != null ">
                and exists (
                    select 1 from sys_dept d 
                    where (d.dept_id = r.dept_id 
                        and (d.dept_id = #{deptId} or find_in_set(#{deptId}, d.ancestors))
                    )
                )
            </if>
            <if test="projectId != null ">and r.project_id = #{projectId}</if>
            <if test="ruleId != null or categoryId != null">
                and (
                    <if test="ruleId != null">
                        r.rule_id = #{ruleId}
                    </if>
                    <if test="ruleId != null and categoryId != null">
                        or
                    </if>
                    <if test="categoryId != null">
                        exists (
                            select 1 
                            from project2score_rule_detail detail
                            join project2score_rule_category rc on detail.category_id = rc.id
                            where detail.id = r.rule_id
                            and (
                                rc.id = #{categoryId}
                                or find_in_set(#{categoryId}, rc.ancestors)
                                or rc.ancestors like concat(#{categoryId}, ',%')
                                or rc.ancestors like concat('%,', #{categoryId}, ',%')
                                or rc.ancestors like concat('%,', #{categoryId})
                            )
                        )
                    </if>
                )
            </if>
            <if test="score != null ">and r.score = #{score}</if>
            <if test="type != null  and type != ''">and r.type = #{type}</if>
            <if test="params.beginProjectTime != null and params.beginProjectTime != ''"><!-- 开始项目时间 -->
                and r.project_time &gt;= #{params.beginProjectTime}
            </if>
            <if test="params.endProjectTime != null and params.endProjectTime != ''"><!-- 结束项目时间 -->
                and r.project_time &lt;= #{params.endProjectTime}
            </if>
        </where>
        order by r.create_time desc
    </select>

    <!-- 查询积分记录详细信息 -->
    <select id="selectProject2scoreRecordVOById" parameterType="Long" resultType="com.ruoyi.web.domain.vo.Project2scoreRecordVO">
        select r.id,
               r.user_id,
               u.nick_name,
               r.dept_id,
               d.dept_name,
               r.project_id,
               p.apply_name as project_name,
               r.rule_id,
               rd.rule_name,
               r.score,
               r.type,
               r.remark,
               r.project_time,
               r.create_time
        from project2score_record r
        left join sys_user u on u.user_id = r.user_id
        left join sys_dept d on d.dept_id = r.dept_id
        left join project2score p on p.id = r.project_id
        left join project2score_rule_detail rd on rd.id = r.rule_id
        where r.id = #{id}
    </select>

    <!-- 查询导出数据列表 -->
    <select id="selectExportList" parameterType="Project2scoreRecord" resultMap="ExportResultMap">
        SELECT 
            u.nick_name,
            d.dept_name,
            p.apply_name as project_name,
            r.project_time,
            p.is_team,
            CASE 
                WHEN p.is_team = '1' THEN pd.is_leader 
                ELSE '0' 
            END as is_leader,
            rd.rule_name,
            r.score,
            r.remark
        FROM project2score_record r
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        LEFT JOIN sys_dept d ON r.dept_id = d.dept_id
        LEFT JOIN project2score p ON r.project_id = p.id
        LEFT JOIN project2score_rule_detail rd ON r.rule_id = rd.id
        LEFT JOIN project2score_rule_category c ON rd.category_id = c.id
        LEFT JOIN project2score_distribution pd ON (p.id = pd.project_id AND pd.user_id = r.user_id)
        <where>
            <if test="userId != null">
                AND r.user_id = #{userId}
            </if>
            <if test="nickName != null and nickName != ''">
                AND (u.nick_name like concat('%', #{nickName}, '%') or u.user_name like concat('%', #{nickName}, '%'))
            </if>
            <if test="deptId != null">
                AND exists (
                    select 1 from sys_dept d2 
                    where (d2.dept_id = r.dept_id 
                        and (d2.dept_id = #{deptId} or find_in_set(#{deptId}, d2.ancestors))
                    )
                )
            </if>
            <if test="ruleId != null or categoryId != null">
                AND (
                    <if test="ruleId != null">
                        r.rule_id = #{ruleId}
                    </if>
                    <if test="ruleId != null and categoryId != null">
                        OR
                    </if>
                    <if test="categoryId != null">
                        exists (
                            select 1 
                            from project2score_rule_detail detail
                            join project2score_rule_category rc on detail.category_id = rc.id
                            where detail.id = r.rule_id
                            and (
                                rc.id = #{categoryId}
                                or find_in_set(#{categoryId}, rc.ancestors)
                                or rc.ancestors like concat(#{categoryId}, ',%')
                                or rc.ancestors like concat('%,', #{categoryId}, ',%')
                                or rc.ancestors like concat('%,', #{categoryId})
                            )
                        )
                    </if>
                )
            </if>
            <if test="type != null and type != ''">
                AND r.type = #{type}
            </if>
            <if test="params.beginProjectTime != null and params.beginProjectTime != ''">
                AND r.project_time &gt;= #{params.beginProjectTime}
            </if>
            <if test="params.endProjectTime != null and params.endProjectTime != ''">
                AND r.project_time &lt;= #{params.endProjectTime}
            </if>
        </where>
        <choose>
            <when test="params != null and params.orderBy != null and params.orderBy != ''">
                ORDER BY ${params.orderBy}
            </when>
            <otherwise>
                ORDER BY r.project_time DESC
            </otherwise>
        </choose>
    </select>
</mapper>