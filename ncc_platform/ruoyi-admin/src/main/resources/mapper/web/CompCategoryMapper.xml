<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.CompCategoryMapper">
    
    <resultMap type="CompCategory" id="CompCategoryResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="fatherId"    column="father_id"    />
    </resultMap>

    <sql id="selectCompCategoryVo">
        select id, name, status, father_id from comp_category
    </sql>

    <select id="selectCompCategoryList" parameterType="CompCategory" resultMap="CompCategoryResult">
        <include refid="selectCompCategoryVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectCompCategoryById" parameterType="Long" resultMap="CompCategoryResult">
        <include refid="selectCompCategoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCompCategory" parameterType="CompCategory"  useGeneratedKeys="true" keyProperty="id">
        insert into comp_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="status != null">status,</if>
            <if test="fatherId != null">father_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="fatherId != null">#{fatherId},</if>
         </trim>
    </insert>

    <update id="updateCompCategory" parameterType="CompCategory">
        update comp_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="fatherId != null">father_id = #{fatherId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCompCategoryById" parameterType="Long">
        delete from comp_category where id = #{id}
    </delete>

    <delete id="deleteCompCategoryByIds" parameterType="String">
        delete from comp_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>