<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.CompInstructorMapper">
    
    <resultMap type="CompInstructor" id="CompInstructorResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="compId"    column="comp_id"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCompInstructorVo">
        select id, teacher_id, dept_id, comp_id, remark from comp_instructor
    </sql>

    <select id="selectCompInstructorList" parameterType="CompInstructor" resultMap="CompInstructorResult">
        <include refid="selectCompInstructorVo"/>
        <where>  
            <if test="teacherId != null  and teacherId != ''"> and teacher_id = #{teacherId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="compId != null "> and comp_id = #{compId}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectCompInstructorById" parameterType="Long" resultMap="CompInstructorResult">
        <include refid="selectCompInstructorVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCompInstructor" parameterType="CompInstructor" useGeneratedKeys="true" keyProperty="id">
        insert into comp_instructor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null and teacherId != ''">teacher_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="compId != null">comp_id,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null and teacherId != ''">#{teacherId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="compId != null">#{compId},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCompInstructor" parameterType="CompInstructor">
        update comp_instructor
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null and teacherId != ''">teacher_id = #{teacherId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="compId != null">comp_id = #{compId},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCompInstructorById" parameterType="Long">
        delete from comp_instructor where id = #{id}
    </delete>

    <delete id="deleteCompInstructorByIds" parameterType="String">
        delete from comp_instructor where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>