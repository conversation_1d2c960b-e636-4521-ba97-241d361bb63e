<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.ScoreAnalysisMapper">
    
    <!-- 获取用户年度积分统计 -->
    <select id="selectUserYearlyStats" resultType="com.ruoyi.web.domain.vo.ScoreAnalysisVO">
        SELECT 
            COALESCE(user_stats.total_score, 0) as totalScore,
            user_stats.school_rank as schoolRank,
            COALESCE(user_stats.total_users, 0) as totalUsers,
            dept_rank.dept_rank as deptRank,
            dept_rank.dept_total as deptTotal,
            COALESCE(user_stats.project_count, 0) as projectCount
        FROM sys_user su
        LEFT JOIN (
            SELECT 
                t2.user_id,
                t2.total_score,
                t2.project_count,
                t2.total_users,
                CASE 
                    WHEN t2.total_score > 0 THEN t2.rank_num 
                    WHEN t2.total_score = 0 THEN t2.total_users
                    ELSE NULL 
                END as school_rank
            FROM (
                SELECT 
                    t1.*,
                    @curRank := IF(@prevScore = t1.total_score, @curRank, @rowNum) as rank_num,
                    @rowNum := @rowNum + 1,
                    @prevScore := t1.total_score
                FROM (
                    SELECT 
                        su.user_id,
                        COALESCE(SUM(r.score), 0) as total_score,
                        COUNT(DISTINCT r.project_id) as project_count,
                        (SELECT COUNT(*) FROM sys_user WHERE del_flag = '0') as total_users
                    FROM sys_user su
                    LEFT JOIN project2score_record r ON su.user_id = r.user_id 
                        AND YEAR(r.project_time) = #{year}
                    WHERE su.del_flag = '0'
                    GROUP BY su.user_id
                    ORDER BY total_score DESC
                ) t1,
                (SELECT @curRank := 1, @prevScore := NULL, @rowNum := 1) r
            ) t2
        ) user_stats ON su.user_id = user_stats.user_id
        LEFT JOIN (
            SELECT 
                t2.user_id,
                t2.dept_id,
                t2.rank_num as dept_rank,
                t2.dept_total
            FROM (
                SELECT 
                    t1.*,
                    CASE 
                        WHEN t1.total_score = 0 THEN t1.dept_total
                        ELSE @deptRank := IF(@prevScore = t1.total_score, @deptRank, @deptRowNum)
                    END as rank_num,
                    @deptRowNum := IF(t1.total_score > 0, @deptRowNum + 1, @deptRowNum),
                    @prevScore := t1.total_score
                FROM (
                    SELECT 
                        su.user_id,
                        su.dept_id,
                        COALESCE(SUM(r.score), 0) as total_score,
                        (SELECT COUNT(*) FROM sys_user WHERE del_flag = '0' AND dept_id = su.dept_id) as dept_total
                    FROM sys_user su
                    LEFT JOIN project2score_record r ON su.user_id = r.user_id 
                        AND YEAR(r.project_time) = #{year}
                    WHERE su.del_flag = '0'
                        AND su.dept_id = (SELECT dept_id FROM sys_user WHERE user_id = #{userId})
                    GROUP BY su.user_id, su.dept_id
                    ORDER BY total_score DESC
                ) t1,
                (SELECT @deptRank := 0, @prevScore := NULL, @deptRowNum := 1) r
            ) t2
        ) dept_rank ON su.user_id = dept_rank.user_id
        WHERE su.user_id = #{userId}
    </select>
    
    <!-- 获取用户上一年度积分统计及排名 -->
    <select id="selectUserLastYearStats" resultType="com.ruoyi.web.domain.vo.ScoreAnalysisVO">
        SELECT 
            COALESCE(user_stats.total_score, 0) as totalScore,
            user_stats.school_rank as schoolRank,
            COALESCE(user_stats.total_users, 0) as totalUsers,
            dept_rank.dept_rank as deptRank,
            dept_rank.dept_total as deptTotal,
            COALESCE(user_stats.project_count, 0) as projectCount
        FROM sys_user su
        LEFT JOIN (
            SELECT 
                t2.user_id,
                t2.total_score,
                t2.project_count,
                t2.total_users,
                CASE 
                    WHEN t2.total_score > 0 THEN t2.rank_num 
                    WHEN t2.total_score = 0 THEN t2.total_users
                    ELSE NULL 
                END as school_rank
            FROM (
                SELECT 
                    t1.*,
                    @curRank := IF(@prevScore = t1.total_score, @curRank, @rowNum) as rank_num,
                    @rowNum := @rowNum + 1,
                    @prevScore := t1.total_score
                FROM (
                    SELECT 
                        su.user_id,
                        COALESCE(SUM(r.score), 0) as total_score,
                        COUNT(DISTINCT r.project_id) as project_count,
                        (SELECT COUNT(*) FROM sys_user WHERE del_flag = '0') as total_users
                    FROM sys_user su
                    LEFT JOIN project2score_record r ON su.user_id = r.user_id 
                        AND YEAR(r.project_time) = #{year} - 1
                    WHERE su.del_flag = '0'
                    GROUP BY su.user_id
                    ORDER BY total_score DESC
                ) t1,
                (SELECT @curRank := 1, @prevScore := NULL, @rowNum := 1) r
            ) t2
        ) user_stats ON su.user_id = user_stats.user_id
        LEFT JOIN (
            SELECT 
                t2.user_id,
                t2.dept_id,
                t2.rank_num as dept_rank,
                t2.dept_total
            FROM (
                SELECT 
                    t1.*,
                    CASE 
                        WHEN t1.total_score = 0 THEN t1.dept_total
                        ELSE @deptRank := IF(@prevScore = t1.total_score, @deptRank, @deptRowNum)
                    END as rank_num,
                    @deptRowNum := IF(t1.total_score > 0, @deptRowNum + 1, @deptRowNum),
                    @prevScore := t1.total_score
                FROM (
                    SELECT 
                        su.user_id,
                        su.dept_id,
                        COALESCE(SUM(r.score), 0) as total_score,
                        (SELECT COUNT(*) FROM sys_user WHERE del_flag = '0' AND dept_id = su.dept_id) as dept_total
                    FROM sys_user su
                    LEFT JOIN project2score_record r ON su.user_id = r.user_id 
                        AND YEAR(r.project_time) = #{year} - 1
                    WHERE su.del_flag = '0'
                        AND su.dept_id = (SELECT dept_id FROM sys_user WHERE user_id = #{userId})
                    GROUP BY su.user_id, su.dept_id
                    ORDER BY total_score DESC
                ) t1,
                (SELECT @deptRank := 0, @prevScore := NULL, @deptRowNum := 1) r
            ) t2
        ) dept_rank ON su.user_id = dept_rank.user_id
        WHERE su.user_id = #{userId}
    </select>
    
    <!-- 获取用户月度积分趋势 -->
    <select id="selectUserMonthlyTrend" resultType="com.ruoyi.web.domain.vo.ScoreTrendVO">
        SELECT 
            CONCAT(MONTH(r.project_time), '月') as timeLabel,
            COALESCE(SUM(r.score), 0) as score
        FROM project2score_record r
        WHERE r.user_id = #{userId}
        AND YEAR(r.project_time) = #{year}
        GROUP BY MONTH(r.project_time)
        ORDER BY MONTH(r.project_time)
    </select>
    
    <!-- 获取用户季度积分趋势 -->
    <select id="selectUserQuarterlyTrend" resultType="com.ruoyi.web.domain.vo.ScoreTrendVO">
        SELECT 
            CONCAT(QUARTER(r.project_time), '季度') as timeLabel,
            COALESCE(SUM(r.score), 0) as score
        FROM project2score_record r
        WHERE r.user_id = #{userId}
        AND YEAR(r.project_time) = #{year}
        GROUP BY QUARTER(r.project_time)
        ORDER BY QUARTER(r.project_time)
    </select>
    
    <!-- 获取用户项目类型分布 -->
    <select id="selectUserProjectTypeDistribution" resultType="com.ruoyi.web.domain.vo.ScoreTypeDistributionVO">
        SELECT 
            parent.category_name as typeName,
            COUNT(DISTINCT p.id) as count,
            ROUND(COUNT(DISTINCT p.id) * 100.0 / (
                SELECT COUNT(DISTINCT p.id)
                FROM project2score p
                LEFT JOIN project2score_distribution pd ON p.id = pd.project_id
                WHERE (p.user_id = #{userId} OR pd.user_id = #{userId})
                AND YEAR(p.project_time) = #{year}
                AND p.status = '2'
            ), 2) as percentage
        FROM project2score p
        LEFT JOIN project2score_distribution pd ON p.id = pd.project_id
        LEFT JOIN project2score_rule_detail d ON p.rule_id = d.id
        LEFT JOIN project2score_rule_category c ON d.category_id = c.id
        LEFT JOIN project2score_rule_category parent ON (
            CASE 
                WHEN c.parent_id = 0 THEN c.id
                ELSE SUBSTRING_INDEX(SUBSTRING_INDEX(c.ancestors, ',', 2), ',', -1)
            END = parent.id
        )
        WHERE (p.user_id = #{userId} OR pd.user_id = #{userId})
        AND YEAR(p.project_time) = #{year}
        AND p.status = '2'
        AND parent.parent_id = 0  -- 只统计二级分类
        GROUP BY parent.id, parent.category_name
        ORDER BY count DESC
    </select>
    
    <!-- 获取部门年度积分统计 -->
    <select id="selectDeptYearlyStats" resultType="com.ruoyi.web.domain.vo.DeptScoreAnalysisVO">
        SELECT 
            COALESCE(current_dept.total_score, 0) as totalScore,
            COALESCE(current_dept.avg_score, 0) as avgScore,
            COALESCE(current_dept.dept_rank, total_depts.cnt) as rank,
            total_depts.cnt as totalDepts,
            COALESCE(current_dept.project_count, 0) as projectCount
        FROM (
            SELECT 
                stats.*,
                @rank := IF(stats.total_score > 0, @rank + 1, @rank) as dept_rank
            FROM (
                SELECT 
                    d1.dept_id,
                    SUM(COALESCE(r.score, 0)) as total_score,
                    ROUND(AVG(dept_avg.avg_score), 2) as avg_score,
                    COUNT(DISTINCT r.project_id) as project_count
                FROM sys_dept d1
                LEFT JOIN (
                    -- 获取所有子部门
                    SELECT 
                        d2.dept_id,
                        FIND_IN_SET(d2.dept_id, d2.ancestors) as level
                    FROM sys_dept d2
                    WHERE FIND_IN_SET(#{deptId}, d2.ancestors) > 0
                    OR d2.dept_id = #{deptId}
                ) child_depts ON 1=1
                LEFT JOIN sys_user su ON su.dept_id = child_depts.dept_id
                LEFT JOIN project2score_record r ON su.user_id = r.user_id 
                    AND YEAR(r.project_time) = #{year}
                LEFT JOIN (
                    -- 计算各部门的人均积分
                    SELECT 
                        su2.dept_id,
                        ROUND(COALESCE(SUM(r2.score) / COUNT(DISTINCT r2.user_id), 0), 2) as avg_score
                    FROM sys_user su2
                    LEFT JOIN project2score_record r2 ON su2.user_id = r2.user_id 
                        AND YEAR(r2.project_time) = #{year}
                    GROUP BY su2.dept_id
                ) dept_avg ON dept_avg.dept_id = child_depts.dept_id
                WHERE d1.parent_id = (
                    SELECT parent_id
                    FROM sys_dept
                    WHERE dept_id = #{deptId}
                )
                AND d1.del_flag = '0'
                GROUP BY d1.dept_id
                ORDER BY total_score DESC
            ) stats,
            (SELECT @rank := 0) r
            WHERE stats.dept_id = #{deptId}
        ) current_dept
        CROSS JOIN (
            SELECT COUNT(*) as cnt
            FROM sys_dept
            WHERE parent_id = (
                SELECT parent_id
                FROM sys_dept
                WHERE dept_id = #{deptId}
            )
            AND del_flag = '0'
        ) total_depts
    </select>
    
    <!-- 获取部门上一年度积分统计 -->
    <select id="selectDeptLastYearStats" resultType="com.ruoyi.web.domain.vo.DeptScoreAnalysisVO">
        SELECT 
            COALESCE(current_dept.total_score, 0) as totalScore,
            COALESCE(current_dept.avg_score, 0) as avgScore,
            COALESCE(current_dept.dept_rank, total_depts.cnt) as rank,
            total_depts.cnt as totalDepts,
            COALESCE(current_dept.project_count, 0) as projectCount
        FROM (
            SELECT 
                stats.*,
                @rank := IF(stats.total_score > 0, @rank + 1, @rank) as dept_rank
            FROM (
                SELECT 
                    d1.dept_id,
                    SUM(COALESCE(r.score, 0)) as total_score,
                    ROUND(AVG(dept_avg.avg_score), 2) as avg_score,
                    COUNT(DISTINCT r.project_id) as project_count
                FROM sys_dept d1
                LEFT JOIN (
                    -- 获取所有子部门
                    SELECT 
                        d2.dept_id,
                        FIND_IN_SET(d2.dept_id, d2.ancestors) as level
                    FROM sys_dept d2
                    WHERE FIND_IN_SET(#{deptId}, d2.ancestors) > 0
                    OR d2.dept_id = #{deptId}
                ) child_depts ON 1=1
                LEFT JOIN sys_user su ON su.dept_id = child_depts.dept_id
                LEFT JOIN project2score_record r ON su.user_id = r.user_id 
                    AND YEAR(r.project_time) = #{year} - 1
                LEFT JOIN (
                    -- 计算各部门的人均积分
                    SELECT 
                        su2.dept_id,
                        ROUND(COALESCE(SUM(r2.score) / COUNT(DISTINCT r2.user_id), 0), 2) as avg_score
                    FROM sys_user su2
                    LEFT JOIN project2score_record r2 ON su2.user_id = r2.user_id 
                        AND YEAR(r2.project_time) = #{year} - 1
                    GROUP BY su2.dept_id
                ) dept_avg ON dept_avg.dept_id = child_depts.dept_id
                WHERE d1.parent_id = (
                    SELECT parent_id
                    FROM sys_dept
                    WHERE dept_id = #{deptId}
                )
                AND d1.del_flag = '0'
                GROUP BY d1.dept_id
                ORDER BY total_score DESC
            ) stats,
            (SELECT @rank := 0) r
            WHERE stats.dept_id = #{deptId}
        ) current_dept
        CROSS JOIN (
            SELECT COUNT(*) as cnt
            FROM sys_dept
            WHERE parent_id = (
                SELECT parent_id
                FROM sys_dept
                WHERE dept_id = #{deptId}
            )
            AND del_flag = '0'
        ) total_depts
    </select>
    
    <!-- 获取部门月度积分趋势 -->
    <select id="selectDeptMonthlyTrend" resultType="com.ruoyi.web.domain.vo.ScoreTrendVO">
        SELECT 
            CONCAT(MONTH(r.project_time), '月') as timeLabel,
            COALESCE(SUM(r.score), 0) as score
        FROM sys_user su
        JOIN (
            -- 获取所有子部门
            SELECT 
                d.dept_id
            FROM sys_dept d
            WHERE FIND_IN_SET(#{deptId}, d.ancestors) > 0
            OR d.dept_id = #{deptId}
        ) child_depts ON su.dept_id = child_depts.dept_id
        LEFT JOIN project2score_record r ON su.user_id = r.user_id
        WHERE YEAR(r.project_time) = #{year}
        GROUP BY MONTH(r.project_time)
        ORDER BY MONTH(r.project_time)
    </select>
    
    <!-- 获取部门季度积分趋势 -->
    <select id="selectDeptQuarterlyTrend" resultType="com.ruoyi.web.domain.vo.ScoreTrendVO">
        SELECT 
            CONCAT(QUARTER(r.project_time), '季度') as timeLabel,
            COALESCE(SUM(r.score), 0) as score
        FROM sys_user su
        JOIN (
            -- 获取所有子部门
            SELECT 
                d.dept_id
            FROM sys_dept d
            WHERE FIND_IN_SET(#{deptId}, d.ancestors) > 0
            OR d.dept_id = #{deptId}
        ) child_depts ON su.dept_id = child_depts.dept_id
        LEFT JOIN project2score_record r ON su.user_id = r.user_id
        WHERE YEAR(r.project_time) = #{year}
        GROUP BY QUARTER(r.project_time)
        ORDER BY QUARTER(r.project_time)
    </select>
    
    <!-- 获取部门人均积分趋势 -->
    <select id="selectDeptAvgScoreTrend" resultType="com.ruoyi.web.domain.vo.ScoreTrendVO">
        SELECT 
            CONCAT(MONTH(r.project_time), '月') as timeLabel,
            ROUND(COALESCE(SUM(r.score) / COUNT(DISTINCT r.user_id), 0), 2) as score
        FROM sys_user su
        JOIN (
            -- 获取所有子部门
            SELECT 
                d.dept_id
            FROM sys_dept d
            WHERE FIND_IN_SET(#{deptId}, d.ancestors) > 0
            OR d.dept_id = #{deptId}
        ) child_depts ON su.dept_id = child_depts.dept_id
        LEFT JOIN project2score_record r ON su.user_id = r.user_id
        WHERE YEAR(r.project_time) = #{year}
        GROUP BY MONTH(r.project_time)
        ORDER BY MONTH(r.project_time)
    </select>
    
    <!-- 获取部门排名趋势 -->
    <select id="selectDeptRankTrend" resultType="com.ruoyi.web.domain.vo.ScoreTrendVO">
        SELECT 
            CONCAT(m.month, '月') as timeLabel,
            COALESCE(m.rank, m.total_depts) as score
        FROM (
            SELECT 
                monthly_stats.month,
                monthly_stats.dept_id,
                monthly_stats.total_depts,
                @rank := IF(@prev_month = monthly_stats.month, 
                    IF(monthly_stats.monthly_score > 0, @rank + 1, @rank),
                    IF(monthly_stats.monthly_score > 0, 1, 0)) as rank,
                @prev_month := monthly_stats.month
            FROM (
                SELECT 
                    MONTH(r.project_time) as month,
                    d1.dept_id,
                    SUM(COALESCE(r.score, 0)) as monthly_score,
                    (
                        SELECT COUNT(*)
                        FROM sys_dept
                        WHERE parent_id = (
                            SELECT parent_id
                            FROM sys_dept
                            WHERE dept_id = #{deptId}
                        )
                        AND del_flag = '0'
                    ) as total_depts
                FROM sys_dept d1
                LEFT JOIN (
                    -- 获取所有子部门
                    SELECT 
                        d2.dept_id
                    FROM sys_dept d2
                    WHERE FIND_IN_SET(#{deptId}, d2.ancestors) > 0
                    OR d2.dept_id = #{deptId}
                ) child_depts ON 1=1
                LEFT JOIN sys_user su ON su.dept_id = child_depts.dept_id
                LEFT JOIN project2score_record r ON su.user_id = r.user_id 
                    AND YEAR(r.project_time) = #{year}
                WHERE d1.parent_id = (
                    SELECT parent_id
                    FROM sys_dept
                    WHERE dept_id = #{deptId}
                )
                AND d1.del_flag = '0'
                GROUP BY MONTH(r.project_time), d1.dept_id
                ORDER BY month, monthly_score DESC
            ) monthly_stats,
            (SELECT @rank := 0, @prev_month := NULL) vars
        ) m
        WHERE m.dept_id = #{deptId}
        ORDER BY m.month
    </select>
    
    <!-- 获取部门项目类型分布 -->
    <select id="selectDeptProjectTypeDistribution" resultType="com.ruoyi.web.domain.vo.ScoreTypeDistributionVO">
        SELECT 
            parent.category_name as typeName,
            COUNT(DISTINCT p.id) as count,
            ROUND(COUNT(DISTINCT p.id) * 100.0 / (
                SELECT COUNT(DISTINCT p.id)
                FROM project2score p
                LEFT JOIN project2score_distribution pd ON p.id = pd.project_id
                JOIN sys_user su2 ON (p.user_id = su2.user_id OR pd.user_id = su2.user_id)
                JOIN (
                    -- 获取所有子部门
                    SELECT 
                        d.dept_id
                    FROM sys_dept d
                    WHERE FIND_IN_SET(#{deptId}, d.ancestors) > 0
                    OR d.dept_id = #{deptId}
                ) child_depts ON su2.dept_id = child_depts.dept_id
                WHERE YEAR(p.project_time) = #{year}
                AND p.status = '2'
            ), 2) as percentage
        FROM project2score p
        LEFT JOIN project2score_distribution pd ON p.id = pd.project_id
        JOIN sys_user su ON (p.user_id = su.user_id OR pd.user_id = su.user_id)
        JOIN (
            -- 获取所有子部门
            SELECT 
                d.dept_id
            FROM sys_dept d
            WHERE FIND_IN_SET(#{deptId}, d.ancestors) > 0
            OR d.dept_id = #{deptId}
        ) child_depts ON su.dept_id = child_depts.dept_id
        LEFT JOIN project2score_rule_detail d ON p.rule_id = d.id
        LEFT JOIN project2score_rule_category c ON d.category_id = c.id
        LEFT JOIN project2score_rule_category parent ON (
            CASE 
                WHEN c.parent_id = 0 THEN c.id
                ELSE SUBSTRING_INDEX(SUBSTRING_INDEX(c.ancestors, ',', 2), ',', -1)
            END = parent.id
        )
        WHERE YEAR(p.project_time) = #{year}
        AND p.status = '2'
        AND parent.parent_id = 0  -- 只统计二级分类
        GROUP BY parent.id, parent.category_name
        ORDER BY count DESC
    </select>
    
</mapper> 