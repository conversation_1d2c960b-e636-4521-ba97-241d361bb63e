<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.ScoreCompletionMapper">

    <resultMap type="com.ruoyi.web.domain.vo.ScoreCompletionVO" id="ScoreCompletionResult">
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="roleNames"    column="role_names"    />
        <result property="totalScore"    column="total_score"    />
        <result property="targetScore"    column="target_score"    />
        <result property="completionStatus"    column="completion_status"    />
        <result property="lastScoreTime"    column="last_score_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="job"    column="job"    />
        <result property="ranks"    column="ranks"    />
        <result property="no"    column="no"    />
    </resultMap>

    <select id="selectScoreCompletionList" parameterType="map" resultMap="ScoreCompletionResult">
        SELECT
            u.user_id,
            u.user_name,
            u.nick_name,
            u.dept_id,
            d.dept_name,
            u.job,
            u.ranks,
            u.no,
            GROUP_CONCAT(DISTINCT r.role_name) AS role_names,
            IFNULL((
                SELECT SUM(CASE WHEN sr.type = '0' THEN sr.score ELSE -sr.score END)
                FROM project2score_record sr
                WHERE sr.user_id = u.user_id
                <if test="beginTime != null and beginTime != ''">
                    AND sr.project_time &gt;= #{beginTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    AND sr.project_time &lt;= #{endTime}
                </if>
            ), 0) AS total_score,
            NULL AS target_score,
            NULL AS completion_status,
            (
                SELECT MAX(sr.project_time)
                FROM project2score_record sr
                WHERE sr.user_id = u.user_id
            ) AS last_score_time
        FROM
            sys_user u
        LEFT JOIN
            sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN
            sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN
            sys_role r ON ur.role_id = r.role_id
        WHERE
            u.del_flag = '0'
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN (
                SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET(#{deptId}, ancestors)
            ))
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="nickName != null and nickName != ''">
            AND u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="roleId != null and roleId != ''">
            AND r.role_id = #{roleId}
        </if>
        <if test="roleIds != null and roleIds != ''">
            AND r.role_id IN
            <foreach collection="roleIds.split(',')" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
        <if test="job != null and job != ''">
            AND (
                <foreach collection="job.split(',')" item="jobItem" separator=" OR ">
                    u.job = #{jobItem} OR u.job LIKE CONCAT(#{jobItem}, '%') OR u.job LIKE CONCAT('%', #{jobItem}) OR u.job LIKE CONCAT('%', #{jobItem}, '%')
                </foreach>
            )
        </if>
        <if test="ranks != null and ranks != ''">
            AND (
                <foreach collection="ranks.split(',')" item="ranksItem" separator=" OR ">
                    u.ranks = #{ranksItem} OR u.ranks LIKE CONCAT(#{ranksItem}, '%') OR u.ranks LIKE CONCAT('%', #{ranksItem}) OR u.ranks LIKE CONCAT('%', #{ranksItem}, '%')
                </foreach>
            )
        </if>
        <if test="no != null and no != ''">
            AND u.no like concat('%', #{no}, '%')
        </if>
        GROUP BY u.user_id, u.user_name, u.nick_name, u.dept_id, d.dept_name, u.job, u.ranks, u.no
        ORDER BY total_score DESC
    </select>

    <select id="selectScoreDetailsList" resultType="com.ruoyi.web.domain.Project2scoreRecord">
        SELECT
            sr.id,
            sr.user_id,
            sr.dept_id,
            sr.project_id,
            sr.rule_id,
            sr.score,
            sr.type,
            sr.remark,
            sr.project_time,
            sr.create_time,
            u.nick_name,
            d.dept_name,
            p.apply_name AS project_name,
            rd.rule_name
        FROM
            project2score_record sr
        LEFT JOIN
            sys_user u ON sr.user_id = u.user_id
        LEFT JOIN
            sys_dept d ON sr.dept_id = d.dept_id
        LEFT JOIN
            project2score p ON sr.project_id = p.id
        LEFT JOIN
            project2score_rule_detail rd ON sr.rule_id = rd.id
        WHERE
            sr.user_id = #{userId}
        <if test="params.beginTime != null and params.beginTime != ''">
            AND sr.project_time &gt;= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND sr.project_time &lt;= #{params.endTime}
        </if>
        ORDER BY
            sr.project_time DESC
    </select>

    <select id="countScoreDetails" resultType="int">
        SELECT
            COUNT(1)
        FROM
            project2score_record sr
        WHERE
            sr.user_id = #{userId}
        <if test="params.beginTime != null and params.beginTime != ''">
            AND sr.project_time &gt;= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND sr.project_time &lt;= #{params.endTime}
        </if>
    </select>
</mapper>