<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.Project2scoreAuditRecordMapper">
    
    <resultMap type="Project2scoreAuditRecord" id="Project2scoreAuditRecordResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="auditUserId"    column="audit_user_id"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectProject2scoreAuditRecordVo">
        select id, project_id, audit_user_id, audit_status, audit_remark, create_time from project2score_audit_record
    </sql>

    <select id="selectProject2scoreAuditRecordList" parameterType="Project2scoreAuditRecord" resultMap="Project2scoreAuditRecordResult">
        <include refid="selectProject2scoreAuditRecordVo"/>
        <where>  
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="auditUserId != null "> and audit_user_id = #{auditUserId}</if>
            <if test="auditStatus != null  and auditStatus != ''"> and audit_status = #{auditStatus}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and audit_remark = #{auditRemark}</if>
        </where>
    </select>
    
    <select id="selectProject2scoreAuditRecordById" parameterType="Long" resultMap="Project2scoreAuditRecordResult">
        <include refid="selectProject2scoreAuditRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertProject2scoreAuditRecord" parameterType="Project2scoreAuditRecord" useGeneratedKeys="true" keyProperty="id">
        insert into project2score_audit_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="auditStatus != null and auditStatus != ''">audit_status,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="auditStatus != null and auditStatus != ''">#{auditStatus},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateProject2scoreAuditRecord" parameterType="Project2scoreAuditRecord">
        update project2score_audit_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="auditStatus != null and auditStatus != ''">audit_status = #{auditStatus},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProject2scoreAuditRecordById" parameterType="Long">
        delete from project2score_audit_record where id = #{id}
    </delete>

    <delete id="deleteProject2scoreAuditRecordByIds" parameterType="String">
        delete from project2score_audit_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectLatestAuditRecord" parameterType="Long" resultMap="Project2scoreAuditRecordResult">
        <include refid="selectProject2scoreAuditRecordVo"/>
        where project_id = #{projectId}
        order by create_time desc
        limit 1
    </select>
</mapper>