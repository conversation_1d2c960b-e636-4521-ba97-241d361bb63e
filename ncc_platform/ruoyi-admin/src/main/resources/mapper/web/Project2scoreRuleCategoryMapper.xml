<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.Project2scoreRuleCategoryMapper">
    
    <resultMap type="Project2scoreRuleCategory" id="Project2scoreRuleCategoryResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categorySort"    column="category_sort"    />
        <result property="level"    column="level"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProject2scoreRuleCategoryVo">
        select id, parent_id, ancestors, category_name, category_sort, level, status, remark, create_by, create_time, update_by, update_time from project2score_rule_category
    </sql>

    <select id="selectProject2scoreRuleCategoryList" parameterType="Project2scoreRuleCategory" resultMap="Project2scoreRuleCategoryResult">
        <include refid="selectProject2scoreRuleCategoryVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="categorySort != null "> and category_sort = #{categorySort}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by category_sort asc, create_time desc
    </select>
    
    <select id="selectProject2scoreRuleCategoryById" parameterType="Long" resultMap="Project2scoreRuleCategoryResult">
        <include refid="selectProject2scoreRuleCategoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertProject2scoreRuleCategory" parameterType="Project2scoreRuleCategory" useGeneratedKeys="true" keyProperty="id">
        insert into project2score_rule_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="categorySort != null">category_sort,</if>
            <if test="level != null">level,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="categorySort != null">#{categorySort},</if>
            <if test="level != null">#{level},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProject2scoreRuleCategory" parameterType="Project2scoreRuleCategory">
        update project2score_rule_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="categorySort != null">category_sort = #{categorySort},</if>
            <if test="level != null">level = #{level},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProject2scoreRuleCategoryById" parameterType="Long">
        delete from project2score_rule_category where id = #{id}
    </delete>

    <delete id="deleteProject2scoreRuleCategoryByIds" parameterType="String">
        delete from project2score_rule_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectChildrenByAncestors" parameterType="Project2scoreRuleCategory" resultMap="Project2scoreRuleCategoryResult">
        <include refid="selectProject2scoreRuleCategoryVo"/>
        <where>
            <if test="ancestors != null and ancestors != ''">
                AND (ancestors like concat('%,', #{ancestors}, ',%') 
                    or ancestors like concat('%,', #{ancestors}) 
                    or ancestors = #{ancestors}
                    or id = #{ancestors})
            </if>
        </where>
        order by category_sort
    </select>

    <update id="updateChildrenStatus" parameterType="Project2scoreRuleCategory">
        update project2score_rule_category
        set status = #{status},
            update_time = #{updateTime},
            update_by = #{updateBy}
        where ancestors like concat('%,', #{id}) 
           or ancestors like concat('%,', #{id}, ',%')
    </update>
</mapper>