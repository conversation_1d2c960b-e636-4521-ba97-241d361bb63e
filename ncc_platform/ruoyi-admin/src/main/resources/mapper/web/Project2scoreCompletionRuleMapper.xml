<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.Project2scoreCompletionRuleMapper">

    <resultMap type="Project2scoreCompletionRule" id="Project2scoreCompletionRuleResult">
        <result property="id"    column="id"    />
        <result property="job"    column="job"    />
        <result property="ranks"    column="ranks"    />
        <result property="target"    column="target"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectProject2scoreCompletionRuleVo">
        select id, job, ranks, target, status, create_by, create_time, update_by, update_time, remark from project2score_completion_rule
    </sql>

    <select id="selectProject2scoreCompletionRuleList" parameterType="Project2scoreCompletionRule" resultMap="Project2scoreCompletionRuleResult">
        <include refid="selectProject2scoreCompletionRuleVo"/>
        <where>
            <if test="job != null  and job != ''"> and job = #{job}</if>
            <if test="ranks != null  and ranks != ''"> and ranks = #{ranks}</if>
            <if test="target != null "> and target = #{target}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectProject2scoreCompletionRuleById" parameterType="Long" resultMap="Project2scoreCompletionRuleResult">
        <include refid="selectProject2scoreCompletionRuleVo"/>
        where id = #{id}
    </select>

    <insert id="insertProject2scoreCompletionRule" parameterType="Project2scoreCompletionRule" useGeneratedKeys="true" keyProperty="id">
        insert into project2score_completion_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="job != null">job,</if>
            <if test="ranks != null">ranks,</if>
            <if test="target != null">target,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="job != null">#{job},</if>
            <if test="ranks != null">#{ranks},</if>
            <if test="target != null">#{target},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateProject2scoreCompletionRule" parameterType="Project2scoreCompletionRule">
        update project2score_completion_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="job != null">job = #{job},</if>
            <if test="ranks != null">ranks = #{ranks},</if>
            <if test="target != null">target = #{target},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProject2scoreCompletionRuleById" parameterType="Long">
        delete from project2score_completion_rule where id = #{id}
    </delete>

    <delete id="deleteProject2scoreCompletionRuleByIds" parameterType="String">
        delete from project2score_completion_rule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>