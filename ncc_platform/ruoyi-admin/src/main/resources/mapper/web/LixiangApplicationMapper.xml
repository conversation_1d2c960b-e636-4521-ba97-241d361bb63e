<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.LixiangApplicationMapper">
    
    <resultMap type="LixiangApplication" id="LixiangApplicationResult">
        <result property="id"    column="id"    />
        <result property="competitionId"    column="competition_id"    />
        <result property="competitionInfo"    column="competition_info"    />
        <result property="competitionHistory"    column="competition_history"    />
        <result property="competitionContent"    column="competition_content"    />
        <result property="competitionPromotionPlan"    column="competition_promotion_plan"    />
        <result property="competitionOrganizational"    column="competition_organizational"    />
        <result property="competitionTrainingPlan"    column="competition_training_plan"    />
        <result property="cost0"    column="cost_0"    />
        <result property="cost1"    column="cost_1"    />
        <result property="cost2"    column="cost_2"    />
        <result property="cost3"    column="cost_3"    />
        <result property="cost4"    column="cost_4"    />
        <result property="cost5"    column="cost_5"    />
        <result property="cost6"    column="cost_6"    />
        <result property="cost7"    column="cost_7"    />
        <result property="cost8"    column="cost_8"    />
        <result property="cost9"    column="cost_9"    />
        <result property="cost10"    column="cost_10"    />
        <result property="cost11"    column="cost_11"    />
        <result property="cost12"    column="cost_12"    />
        <result property="cost13"    column="cost_13"    />
        <result property="cost14"    column="cost_14"    />
        <result property="cost15"    column="cost_15"    />
        <result property="cost16"    column="cost_16"    />
        <result property="cost17"    column="cost_17"    />
        <result property="cost18Name"    column="cost_18_name"    />
        <result property="cost18"    column="cost_18"    />
        <result property="cost19Name"    column="cost_19_name"    />
        <result property="cost19"    column="cost_19"    />
        <result property="cost20Name"    column="cost_20_name"    />
        <result property="cost20"    column="cost_20"    />
        <result property="cost21"    column="cost_21"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLixiangApplicationVo">
        select id, competition_id, competition_info, competition_history, competition_content, competition_promotion_plan, competition_organizational, competition_training_plan, cost_0, cost_1, cost_2, cost_3, cost_4, cost_5, cost_6, cost_7, cost_8, cost_9, cost_10, cost_11, cost_12, cost_13, cost_14, cost_15, cost_16, cost_17, cost_18_name, cost_18, cost_19_name, cost_19, cost_20_name, cost_20, cost_21, create_time, update_time from lixiang_application
    </sql>

    <select id="selectLixiangApplicationList" parameterType="LixiangApplication" resultMap="LixiangApplicationResult">
        <include refid="selectLixiangApplicationVo"/>
        <where>  
            <if test="competitionId != null "> and competition_id = #{competitionId}</if>
            <if test="competitionInfo != null  and competitionInfo != ''"> and competition_info = #{competitionInfo}</if>
            <if test="competitionHistory != null  and competitionHistory != ''"> and competition_history = #{competitionHistory}</if>
            <if test="competitionContent != null  and competitionContent != ''"> and competition_content = #{competitionContent}</if>
            <if test="competitionPromotionPlan != null  and competitionPromotionPlan != ''"> and competition_promotion_plan = #{competitionPromotionPlan}</if>
            <if test="competitionOrganizational != null  and competitionOrganizational != ''"> and competition_organizational = #{competitionOrganizational}</if>
            <if test="competitionTrainingPlan != null  and competitionTrainingPlan != ''"> and competition_training_plan = #{competitionTrainingPlan}</if>
        </where>
    </select>
    
    <select id="selectLixiangApplicationById" parameterType="Long" resultMap="LixiangApplicationResult">
        <include refid="selectLixiangApplicationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLixiangApplication" parameterType="LixiangApplication" useGeneratedKeys="true" keyProperty="id">
        insert into lixiang_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="competitionId != null">competition_id,</if>
            <if test="competitionInfo != null">competition_info,</if>
            <if test="competitionHistory != null">competition_history,</if>
            <if test="competitionContent != null">competition_content,</if>
            <if test="competitionPromotionPlan != null">competition_promotion_plan,</if>
            <if test="competitionOrganizational != null">competition_organizational,</if>
            <if test="competitionTrainingPlan != null">competition_training_plan,</if>
            <if test="cost0 != null">cost_0,</if>
            <if test="cost1 != null">cost_1,</if>
            <if test="cost2 != null">cost_2,</if>
            <if test="cost3 != null">cost_3,</if>
            <if test="cost4 != null">cost_4,</if>
            <if test="cost5 != null">cost_5,</if>
            <if test="cost6 != null">cost_6,</if>
            <if test="cost7 != null">cost_7,</if>
            <if test="cost8 != null">cost_8,</if>
            <if test="cost9 != null">cost_9,</if>
            <if test="cost10 != null">cost_10,</if>
            <if test="cost11 != null">cost_11,</if>
            <if test="cost12 != null">cost_12,</if>
            <if test="cost13 != null">cost_13,</if>
            <if test="cost14 != null">cost_14,</if>
            <if test="cost15 != null">cost_15,</if>
            <if test="cost16 != null">cost_16,</if>
            <if test="cost17 != null">cost_17,</if>
            <if test="cost18Name != null">cost_18_name,</if>
            <if test="cost18 != null">cost_18,</if>
            <if test="cost19Name != null">cost_19_name,</if>
            <if test="cost19 != null">cost_19,</if>
            <if test="cost20Name != null">cost_20_name,</if>
            <if test="cost20 != null">cost_20,</if>
            <if test="cost21 != null">cost_21,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="competitionId != null">#{competitionId},</if>
            <if test="competitionInfo != null">#{competitionInfo},</if>
            <if test="competitionHistory != null">#{competitionHistory},</if>
            <if test="competitionContent != null">#{competitionContent},</if>
            <if test="competitionPromotionPlan != null">#{competitionPromotionPlan},</if>
            <if test="competitionOrganizational != null">#{competitionOrganizational},</if>
            <if test="competitionTrainingPlan != null">#{competitionTrainingPlan},</if>
            <if test="cost0 != null">#{cost0},</if>
            <if test="cost1 != null">#{cost1},</if>
            <if test="cost2 != null">#{cost2},</if>
            <if test="cost3 != null">#{cost3},</if>
            <if test="cost4 != null">#{cost4},</if>
            <if test="cost5 != null">#{cost5},</if>
            <if test="cost6 != null">#{cost6},</if>
            <if test="cost7 != null">#{cost7},</if>
            <if test="cost8 != null">#{cost8},</if>
            <if test="cost9 != null">#{cost9},</if>
            <if test="cost10 != null">#{cost10},</if>
            <if test="cost11 != null">#{cost11},</if>
            <if test="cost12 != null">#{cost12},</if>
            <if test="cost13 != null">#{cost13},</if>
            <if test="cost14 != null">#{cost14},</if>
            <if test="cost15 != null">#{cost15},</if>
            <if test="cost16 != null">#{cost16},</if>
            <if test="cost17 != null">#{cost17},</if>
            <if test="cost18Name != null">#{cost18Name},</if>
            <if test="cost18 != null">#{cost18},</if>
            <if test="cost19Name != null">#{cost19Name},</if>
            <if test="cost19 != null">#{cost19},</if>
            <if test="cost20Name != null">#{cost20Name},</if>
            <if test="cost20 != null">#{cost20},</if>
            <if test="cost21 != null">#{cost21},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateLixiangApplication" parameterType="LixiangApplication">
        update lixiang_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="competitionId != null">competition_id = #{competitionId},</if>
            <if test="competitionInfo != null">competition_info = #{competitionInfo},</if>
            <if test="competitionHistory != null">competition_history = #{competitionHistory},</if>
            <if test="competitionContent != null">competition_content = #{competitionContent},</if>
            <if test="competitionPromotionPlan != null">competition_promotion_plan = #{competitionPromotionPlan},</if>
            <if test="competitionOrganizational != null">competition_organizational = #{competitionOrganizational},</if>
            <if test="competitionTrainingPlan != null">competition_training_plan = #{competitionTrainingPlan},</if>
            <if test="cost0 != null">cost_0 = #{cost0},</if>
            <if test="cost1 != null">cost_1 = #{cost1},</if>
            <if test="cost2 != null">cost_2 = #{cost2},</if>
            <if test="cost3 != null">cost_3 = #{cost3},</if>
            <if test="cost4 != null">cost_4 = #{cost4},</if>
            <if test="cost5 != null">cost_5 = #{cost5},</if>
            <if test="cost6 != null">cost_6 = #{cost6},</if>
            <if test="cost7 != null">cost_7 = #{cost7},</if>
            <if test="cost8 != null">cost_8 = #{cost8},</if>
            <if test="cost9 != null">cost_9 = #{cost9},</if>
            <if test="cost10 != null">cost_10 = #{cost10},</if>
            <if test="cost11 != null">cost_11 = #{cost11},</if>
            <if test="cost12 != null">cost_12 = #{cost12},</if>
            <if test="cost13 != null">cost_13 = #{cost13},</if>
            <if test="cost14 != null">cost_14 = #{cost14},</if>
            <if test="cost15 != null">cost_15 = #{cost15},</if>
            <if test="cost16 != null">cost_16 = #{cost16},</if>
            <if test="cost17 != null">cost_17 = #{cost17},</if>
            <if test="cost18Name != null">cost_18_name = #{cost18Name},</if>
            <if test="cost18 != null">cost_18 = #{cost18},</if>
            <if test="cost19Name != null">cost_19_name = #{cost19Name},</if>
            <if test="cost19 != null">cost_19 = #{cost19},</if>
            <if test="cost20Name != null">cost_20_name = #{cost20Name},</if>
            <if test="cost20 != null">cost_20 = #{cost20},</if>
            <if test="cost21 != null">cost_21 = #{cost21},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLixiangApplicationById" parameterType="Long">
        delete from lixiang_application where id = #{id}
    </delete>

    <delete id="deleteLixiangApplicationByIds" parameterType="String">
        delete from lixiang_application where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>